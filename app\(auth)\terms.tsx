import React from 'react';
import { View, Text, ScrollView, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

export default function TermsScreen() {
  const router = useRouter();

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <View className="flex-row items-center px-6 pt-12 pb-4 bg-background border-b border-border">
        <Pressable onPress={handleBack} className="mr-4 p-2">
          <Ionicons name="arrow-back-outline" size={24} color="#7A3E4F" />
        </Pressable>
        <Text className="text-xl font-semibold text-text-primary">
          Terms of Service
        </Text>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 px-6 py-6" showsVerticalScrollIndicator={false}>
        <Text className="mb-6 text-2xl font-bold text-text-primary">
          Aril Terms of Service
        </Text>

        <Text className="mb-4 text-sm text-text-secondary">
          Last updated: {new Date().toLocaleDateString()}
        </Text>

        <View className="space-y-6">
          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              1. Acceptance of Terms
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              By accessing and using Aril, you accept and agree to be bound by the terms and provision of this agreement.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              2. Use License
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              Permission is granted to temporarily download one copy of Aril for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              3. User Accounts
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              You are responsible for safeguarding the password and for maintaining the confidentiality of your account. You agree not to disclose your password to any third party.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              4. Privacy Policy
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our service.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              5. Prohibited Uses
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts. You may not violate any international, federal, provincial, or state regulations, rules, or laws.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              6. Content
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              Our service allows you to post, link, store, share and otherwise make available certain information, text, graphics, or other material. You are responsible for the content that you post to the service.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              7. Termination
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              8. Changes to Terms
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              9. Contact Information
            </Text>
            <Text className="mb-8 text-base leading-6 text-text-secondary">
              If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

import { useState, useEffect, useCallback } from 'react';
import { SignificantOtherProfile } from '@/functions/src/types/firestore';
import { useAuth } from '@/contexts/AuthContext';
import { getSignificantOthers } from '@/services/profileService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { QueryDocumentSnapshot } from 'firebase/firestore';
import { 
  PROFILES_CACHE_KEY, 
  CACHE_EXPIRY_KEY, 
  PROFILES_LAST_UPDATED_KEY
} from '@/constants/storageKeys';

// Cache constants
const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes

interface UseProfileDataReturn {
  profiles: SignificantOtherProfile[];
  isLoading: boolean;
  error: string | null;
  refreshProfiles: (forceRefresh?: boolean) => Promise<void>;
  loadMoreProfiles: () => Promise<void>;
  hasMore: boolean;
  loadingMore: boolean;
  lastFetchTimestamp: number;
}

/**
 * Hook for managing profile data - fetching, caching, and state management
 * Extracted from useCalendarData god object for single responsibility
 */
const useProfileData = (): UseProfileDataReturn => {
  const [profiles, setProfiles] = useState<SignificantOtherProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTimestamp, setLastFetchTimestamp] = useState<number>(0);
  const [lastVisible, setLastVisible] = useState<QueryDocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  const { user } = useAuth();

  // Cache management utilities
  const updateCache = async (data: SignificantOtherProfile[]) => {
    try {
      // Include user ID in cache to prevent cross-user data leakage
      const cacheData = {
        userId: user?.uid,
        profiles: data,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(PROFILES_CACHE_KEY, JSON.stringify(cacheData));
      await AsyncStorage.setItem(CACHE_EXPIRY_KEY, Date.now().toString());
    } catch (error) {
      console.error('Error updating profiles cache:', error);
    }
  };

  const isCacheValid = async (): Promise<boolean> => {
    try {
      const expiryTime = await AsyncStorage.getItem(CACHE_EXPIRY_KEY);
      if (!expiryTime) return false;

      const timeDiff = Date.now() - parseInt(expiryTime);
      return timeDiff < CACHE_EXPIRY_TIME;
    } catch (error) {
      return false;
    }
  };

  const loadFromCache = async (): Promise<SignificantOtherProfile[]> => {
    try {
      const cachedProfilesString = await AsyncStorage.getItem(PROFILES_CACHE_KEY);
      if (cachedProfilesString) {
        const cacheData = JSON.parse(cachedProfilesString);

        // SECURITY: Verify cache belongs to current user
        if (cacheData.userId && cacheData.userId === user?.uid && cacheData.profiles) {
          console.log('PROFILE DATA: Using cached profiles for current user');
          return cacheData.profiles;
        } else {
          console.log('PROFILE DATA: Cache belongs to different user, clearing cache');
          await AsyncStorage.removeItem(PROFILES_CACHE_KEY);
          await AsyncStorage.removeItem(CACHE_EXPIRY_KEY);
        }
      }
    } catch (cacheError) {
      console.error('PROFILE DATA: Error reading profiles from cache:', cacheError);
      // Clear potentially corrupted cache
      await AsyncStorage.removeItem(PROFILES_CACHE_KEY);
      await AsyncStorage.removeItem(CACHE_EXPIRY_KEY);
    }
    return [];
  };

  // Main data loading function
  const refreshProfiles = useCallback(async (forceRefresh = false) => {
    if (!user) {
      setError('User not authenticated.');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Check if we can use cached data (only if not forcing refresh)
      const cacheValid = !forceRefresh && (await isCacheValid());
      let profilesResult: SignificantOtherProfile[] = [];

      if (cacheValid) {
        profilesResult = await loadFromCache();
        if (profilesResult.length > 0) {
          setProfiles(profilesResult);
        }
      }

      // If cache wasn't valid or profiles weren't in cache, fetch from network
      if (profilesResult.length === 0 || forceRefresh) {
        console.log('PROFILE DATA: Fetching profiles from network...');
        const { profiles: fetchedProfiles, lastVisible: newLastVisible } = await getSignificantOthers(user.uid, null);
        console.log('PROFILE DATA: Fetched profiles count:', fetchedProfiles?.length || 0);
        
        setProfiles(fetchedProfiles);
        setLastVisible(newLastVisible);
        setHasMore(!!newLastVisible);

        // Update cache
        if (fetchedProfiles.length > 0) {
          await updateCache(fetchedProfiles);
        }
      }

      // Update the last fetch timestamp
      setLastFetchTimestamp(Date.now());
      setError(null);

    } catch (err) {
      console.error('Error refreshing profiles:', err);
      setError('Failed to load profiles. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Initial data load
  useEffect(() => {
    if (user?.uid) {
      refreshProfiles();
    }
  }, [user?.uid, refreshProfiles]);

  const loadMoreProfiles = async () => {
    if (!hasMore || loadingMore || !user) return;

    setLoadingMore(true);
    try {
      const { profiles: newProfiles, lastVisible: newLastVisible } = await getSignificantOthers(user.uid, lastVisible);
      setProfiles(prevProfiles => [...prevProfiles, ...newProfiles]);
      setLastVisible(newLastVisible);
      setHasMore(!!newLastVisible);
    } catch (err) {
      console.error('Error loading more profiles:', err);
      setError('Failed to load more profiles.');
    } finally {
      setLoadingMore(false);
    }
  };

  return {
    profiles,
    isLoading,
    error,
    refreshProfiles,
    loadMoreProfiles,
    hasMore,
    loadingMore,
    lastFetchTimestamp
  };
};

export default useProfileData;
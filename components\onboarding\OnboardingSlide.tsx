import React from "react";
import { View, Text, StyleSheet, useWindowDimensions, ImageBackground, TouchableOpacity } from "react-native";
import Button from "../ui/Button";

interface OnboardingSlideProps {
  item: {
    id: number;
    image: any;
    title: string;
    subtitle: string;
  };
  onGetStarted: () => void;
  onLogin: () => void;
}

const OnboardingSlide: React.FC<OnboardingSlideProps> = ({ item, onGetStarted, onLogin }) => {
  const { width, height } = useWindowDimensions();

  return (
    <View style={[styles.container, { width, height }]}>
        <ImageBackground source={item.image} style={[styles.image, { width, height }]}>
            <View style={styles.contentContainer}>
                <View style={styles.textWrapper}>
                    <Text style={styles.title}>{item.title}</Text>
                    <Text style={styles.subtitle}>{item.subtitle}</Text>
                </View>

                <View style={styles.buttonContainer}>
                    <Button
                        title="Get Started"
                        onPress={onGetStarted}
                        variant="primary"
                    />
                    <TouchableOpacity 
                        style={styles.loginLink}
                        onPress={onLogin} 
                    >
                        <Text style={styles.loginLinkText}>I already have an account</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  image: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  contentContainer: {
    width: '100%',
    paddingHorizontal: 40,
    paddingBottom: 50,
    alignItems: 'center',
  },
  textWrapper: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 15,
    textAlign: "center",
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.6)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 18,
    textAlign: "center",
    paddingHorizontal: 20,
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.6)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 40,
    gap: 15,
  },
  loginLink: {
    alignSelf: 'center',
  },
  loginLinkText: {
      fontSize: 16,
      textDecorationLine: 'underline',
      color: '#FFFFFF',
      textShadowColor: 'rgba(0, 0, 0, 0.6)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 3,
  }
});

export default OnboardingSlide; 
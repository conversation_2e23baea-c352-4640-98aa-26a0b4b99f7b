# Google Authentication Configuration Guide

## Current Status
✅ **Web Client ID**: Configured (EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID)
❌ **iOS Client ID**: Not configured (EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID)
❌ **Android Client ID**: Not configured (EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID)

## Required Environment Variables

Add these to your `.env` file or Expo environment configuration:

```bash
# Google OAuth Configuration
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your-web-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your-ios-client-id.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID=your-android-client-id.apps.googleusercontent.com
```

## Setup Instructions

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API and Google Sign-In API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

### 2. Create OAuth Client IDs

#### For iOS:
- Application type: iOS
- Bundle ID: Your app's bundle identifier (from app.config.js)
- App Store ID: (if published)

#### For Android:
- Application type: Android
- Package name: Your app's package name (from app.config.js)
- SHA-1 certificate fingerprint: 
  - Development: Get from `expo credentials:manager`
  - Production: Get from your keystore

#### For Web (already configured):
- Application type: Web application
- Authorized redirect URIs: Include your Expo development URLs

### 3. Configure app.config.js

Ensure your app.config.js includes the Google configuration:

```javascript
export default {
  expo: {
    // ... other config
    plugins: [
      [
        "@react-native-google-signin/google-signin",
        {
          iosUrlScheme: "your-ios-url-scheme"
        }
      ]
    ],
    ios: {
      googleServicesFile: "./GoogleService-Info.plist"
    },
    android: {
      googleServicesFile: "./google-services.json"
    }
  }
};
```

### 4. Download Configuration Files

- **iOS**: Download `GoogleService-Info.plist` and place in project root
- **Android**: Download `google-services.json` and place in project root

### 5. Test Configuration

After setup, test Google authentication in:
- Expo Go (development)
- Development build
- Production build

## Current Implementation

The Google auth is configured in `contexts/AuthContext.tsx`:

```typescript
const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
  clientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
  iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
  androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
});
```

## Error Handling

The app includes comprehensive error handling for Google auth failures:
- Network errors
- User cancellation
- Configuration errors
- Permission errors

## Security Notes

1. **Client IDs are public** - they can be included in your app bundle
2. **Never include client secrets** in mobile apps
3. **Use Firebase Auth** for secure token validation
4. **Validate tokens server-side** for sensitive operations

## Testing Checklist

- [ ] Web Google sign-in works in development
- [ ] iOS Google sign-in works in development build
- [ ] Android Google sign-in works in development build
- [ ] Google sign-in works in production builds
- [ ] Error handling works for network failures
- [ ] Error handling works for user cancellation
- [ ] Proper user data is received from Google
- [ ] Firebase authentication completes successfully

## Troubleshooting

### Common Issues:

1. **"Invalid client ID"**
   - Check environment variables are set correctly
   - Verify client ID matches Google Cloud Console

2. **"Sign-in failed"**
   - Check bundle ID/package name matches Google Console
   - Verify SHA-1 fingerprint for Android

3. **"Network error"**
   - Check internet connection
   - Verify Google APIs are enabled

4. **Works in development but not production**
   - Check production client IDs are configured
   - Verify production certificates match Google Console

## Next Steps

1. Set up Google Cloud Console project
2. Create iOS and Android OAuth client IDs
3. Add environment variables to project
4. Download and add configuration files
5. Test in development and production builds

{"cli": {"version": ">= 16.13.4", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "environment": "development"}, "preview": {"distribution": "internal", "environment": "preview"}, "production": {"autoIncrement": true, "environment": "production", "env": {"EXPO_PUBLIC_FIREBASE_API_KEY": "${secret.FIREBASE_API_KEY}", "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "${secret.FIREBASE_AUTH_DOMAIN}", "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "${secret.FIREBASE_PROJECT_ID}", "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "${secret.FIREBASE_STORAGE_BUCKET}", "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "${secret.FIREBASE_MESSAGING_SENDER_ID}", "EXPO_PUBLIC_FIREBASE_APP_ID": "${secret.FIREBASE_APP_ID}", "EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID": "${secret.GOOGLE_WEB_CLIENT_ID}"}}}, "submit": {"production": {}}}
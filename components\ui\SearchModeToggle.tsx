import React, { useMemo, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { SearchMode } from '../../hooks/useSearchRecommendations';
import { colors } from '../../constants/Colors';

interface SearchModeToggleProps {
  searchMode: SearchMode;
  onToggle: (mode: SearchMode) => void;
  profileName?: string;
  disabled?: boolean;
}

const SearchModeToggle: React.FC<SearchModeToggleProps> = ({
  searchMode,
  onToggle,
  profileName,
  disabled = false,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const animatedValue = useRef(new Animated.Value(searchMode === 'personalized' ? 1 : 0)).current;

  const toggleMode = () => {
    if (!disabled) {
      onToggle(searchMode === 'generic' ? 'personalized' : 'generic');
    }
  };

  const isPersonalized = searchMode === 'personalized';

  // Animate switch when mode changes
  useEffect(() => {
    Animated.spring(animatedValue, {
      toValue: isPersonalized ? 1 : 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [isPersonalized, animatedValue]);

  // Memoized theme colors
  const themeColors = useMemo(() => ({
    primary: isDark ? colors.primary.dark : colors.primary.DEFAULT,
    primaryLight: isDark ? `${colors.primary.dark}10` : `${colors.primary.DEFAULT}10`,
    primaryBorder: isDark ? `${colors.primary.dark}33` : `${colors.primary.DEFAULT}33`,
    background: isDark ? colors.card.dark : colors.card.DEFAULT,
    textPrimary: isDark ? colors['text-primary'].dark : colors['text-primary'].DEFAULT,
    textSecondary: isDark ? colors['text-secondary'].dark : colors['text-secondary'].DEFAULT,
    border: isDark ? colors.border.dark : colors.border.DEFAULT,
    borderLight: isDark ? '#4B556650' : '#D1D5DB50',
    iconBackground: isDark ? '#374151' : '#F3F4F6',
    iconColor: isDark ? '#9CA3AF' : '#6B7280',
  }), [isDark]);

  // Memoized dynamic styles
  const dynamicStyles = useMemo(() => ({
    toggleButton: {
      ...styles.toggleButton,
      backgroundColor: themeColors.background,
      borderColor: disabled 
        ? themeColors.border
        : isPersonalized
          ? themeColors.primaryBorder
          : themeColors.borderLight,
      opacity: disabled ? 0.5 : 1,
    },
    iconContainer: {
      ...styles.iconContainer,
      backgroundColor: isPersonalized 
        ? themeColors.primaryLight
        : themeColors.iconBackground,
    },
    switch: {
      ...styles.switch,
      backgroundColor: isPersonalized
        ? themeColors.primary
        : (isDark ? '#4B5563' : '#D1D5DB'),
    },
  }), [themeColors, disabled, isPersonalized, isDark]);

  // Animated switch knob style
  const animatedSwitchKnob = {
    ...styles.switchKnob,
    transform: [
      {
        translateX: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0, 28],
        }),
      },
    ],
  };

  // Memoized text styles
  const textStyles = useMemo(() => ({
    instruction: { ...styles.instructionText, color: themeColors.textSecondary },
    title: { ...styles.titleText, color: themeColors.textPrimary },
    subtitle: { ...styles.subtitleText, color: themeColors.textSecondary },
    helper: { ...styles.helperText, color: themeColors.textSecondary },
  }), [themeColors]);

  return (
    <View style={styles.container}>
      {/* Tap instruction */}
      <Text style={textStyles.instruction}>
        {disabled ? 'Search Mode' : 'Tap to toggle search mode'}
      </Text>
      
      <TouchableOpacity
        onPress={toggleMode}
        disabled={disabled}
        style={dynamicStyles.toggleButton}
        activeOpacity={0.8}
        accessibilityRole="switch"
        accessibilityState={{ 
          checked: isPersonalized,
          disabled: disabled
        }}
        accessibilityLabel={`Search mode toggle. Currently ${searchMode}${isPersonalized && profileName ? ` for ${profileName}` : ''}`}
      >
        <View style={styles.contentRow}>
          <View style={dynamicStyles.iconContainer}>
            <Feather
              name={isPersonalized ? 'user' : 'globe'}
              size={18}
              color={isPersonalized ? themeColors.primary : themeColors.iconColor}
            />
          </View>
          
          <View style={styles.textContainer}>
            <Text style={textStyles.title}>
              {isPersonalized ? 'Personalized Search' : 'Generic Search'}
            </Text>
            <Text style={textStyles.subtitle}>
              {isPersonalized
                ? `Tailored for ${profileName || 'selected profile'}`
                : 'General gift recommendations'
              }
            </Text>
          </View>
        </View>
        
        {/* Toggle Switch */}
        <View style={styles.switchContainer}>
          <View style={dynamicStyles.switch}>
            <Animated.View style={animatedSwitchKnob}>
              {/* Mini icon in toggle */}
              <Feather
                name={isPersonalized ? 'user' : 'globe'}
                size={10}
                color={isPersonalized ? themeColors.primary : themeColors.iconColor}
              />
            </Animated.View>
          </View>
          
          {/* Toggle state indicator */}
          {!disabled && (
            <View style={styles.indicators}>
              <View style={[
                styles.indicator,
                { backgroundColor: !isPersonalized ? '#9CA3AF' : '#E5E7EB' }
              ]} />
              <View style={[
                styles.indicator,
                { backgroundColor: isPersonalized ? themeColors.primary : '#E5E7EB' }
              ]} />
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Helper Text */}
      {disabled && (
        <Text style={textStyles.helper}>
          Select a profile to enable personalized search
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 8,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    padding: 8,
    borderRadius: 20,
  },
  textContainer: {
    marginLeft: 12,
    flex: 1,
  },
  titleText: {
    fontWeight: '600',
  },
  subtitleText: {
    fontSize: 14,
  },
  switchContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  switch: {
    width: 56,
    height: 28,
    borderRadius: 14,
    padding: 2,
  },
  switchKnob: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicators: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
  },
  helperText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default SearchModeToggle; 
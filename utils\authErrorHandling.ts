// utils/authErrorHandling.ts

export interface AuthErrorInfo {
  message: string;
  type: 'error' | 'warning' | 'info';
  actionable?: boolean;
  suggestion?: string;
}

export const getAuthErrorInfo = (errorCode: string, errorMessage?: string): AuthErrorInfo => {
  switch (errorCode) {
    // Email/Password Sign In Errors
    case 'auth/user-not-found':
      return {
        message: 'No account found with this email address',
        type: 'error',
        actionable: true,
        suggestion: 'Check your email or create a new account'
      };

    case 'auth/wrong-password':
      return {
        message: 'Incorrect password',
        type: 'error',
        actionable: true,
        suggestion: 'Please try again or reset your password'
      };

    case 'auth/invalid-email':
      return {
        message: 'Please enter a valid email address',
        type: 'error',
        actionable: true
      };

    case 'auth/user-disabled':
      return {
        message: 'This account has been disabled',
        type: 'error',
        actionable: false,
        suggestion: 'Please contact support for assistance'
      };

    case 'auth/too-many-requests':
      return {
        message: 'Too many failed attempts. Please try again later',
        type: 'warning',
        actionable: true,
        suggestion: 'Wait a few minutes before trying again'
      };

    // Sign Up Errors
    case 'auth/email-already-in-use':
      return {
        message: 'An account with this email already exists',
        type: 'error',
        actionable: true,
        suggestion: 'Try signing in instead or use a different email'
      };

    case 'auth/weak-password':
      return {
        message: 'Password is too weak',
        type: 'error',
        actionable: true,
        suggestion: 'Use at least 8 characters with mixed case and numbers'
      };

    case 'auth/operation-not-allowed':
      return {
        message: 'Email/password accounts are not enabled',
        type: 'error',
        actionable: false,
        suggestion: 'Please contact support'
      };

    // Network Errors
    case 'auth/network-request-failed':
      return {
        message: 'Network connection failed',
        type: 'warning',
        actionable: true,
        suggestion: 'Check your internet connection and try again'
      };

    case 'auth/timeout':
      return {
        message: 'Request timed out',
        type: 'warning',
        actionable: true,
        suggestion: 'Please try again'
      };

    // Google Sign-In Errors
    case 'auth/account-exists-with-different-credential':
      return {
        message: 'An account already exists with this email using a different sign-in method',
        type: 'error',
        actionable: true,
        suggestion: 'Try signing in with email/password instead'
      };

    case 'auth/credential-already-in-use':
      return {
        message: 'This Google account is already linked to another user',
        type: 'error',
        actionable: false
      };

    case 'auth/popup-closed-by-user':
    case 'google/expo-auth-error':
      return {
        message: 'Google sign-in was cancelled',
        type: 'info',
        actionable: true,
        suggestion: 'Please try again to continue with Google'
      };

    case 'google/prompt-error':
      return {
        message: 'Unable to start Google sign-in',
        type: 'error',
        actionable: true,
        suggestion: 'Please try again or use email/password instead'
      };

    // Generic/Unknown Errors
    case 'auth/invalid-credential':
      return {
        message: 'Invalid login credentials',
        type: 'error',
        actionable: true,
        suggestion: 'Please check your email and password'
      };

    case 'auth/requires-recent-login':
      return {
        message: 'Please sign in again to continue',
        type: 'warning',
        actionable: true,
        suggestion: 'For security, please log in again'
      };

    default:
      // Fallback for unknown errors
      if (errorMessage?.toLowerCase().includes('network')) {
        return {
          message: 'Connection problem occurred',
          type: 'warning',
          actionable: true,
          suggestion: 'Check your internet connection and try again'
        };
      }

      if (errorMessage?.toLowerCase().includes('timeout')) {
        return {
          message: 'Request timed out',
          type: 'warning',
          actionable: true,
          suggestion: 'Please try again'
        };
      }

      return {
        message: 'Something went wrong. Please try again',
        type: 'error',
        actionable: true,
        suggestion: 'If the problem persists, please contact support'
      };
  }
};

export const getErrorDisplayMessage = (errorCode: string, errorMessage?: string): string => {
  const errorInfo = getAuthErrorInfo(errorCode, errorMessage);
  return errorInfo.suggestion 
    ? `${errorInfo.message}. ${errorInfo.suggestion}`
    : errorInfo.message;
};

// Helper to determine if an error should show a retry button
export const isRetryableError = (errorCode: string): boolean => {
  const retryableErrors = [
    'auth/network-request-failed',
    'auth/timeout',
    'auth/too-many-requests',
    'google/prompt-error',
    'google/expo-auth-error'
  ];
  return retryableErrors.includes(errorCode);
};

// Helper to determine if an error is temporary
export const isTemporaryError = (errorCode: string): boolean => {
  const temporaryErrors = [
    'auth/network-request-failed',
    'auth/timeout',
    'auth/too-many-requests'
  ];
  return temporaryErrors.includes(errorCode);
};

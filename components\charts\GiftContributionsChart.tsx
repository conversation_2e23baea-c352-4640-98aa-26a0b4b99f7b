import React, { useMemo, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  GestureResponderEvent,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  SlideInRight,
  Layout,
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';
import { Timestamp } from 'firebase/firestore';

import { PastGiftGiven } from '../../types/firestore';
import {
  generateContributionsData,
  GiftContributionsData,
  DailyGiftData,
  WeeklyGiftData,
  getIntensityLevel,
  timestampToDate,
} from '../../utils/chartDataUtils';
import ContributionSquare from './ContributionSquare';
import ChartTooltip from './ChartTooltip';
import ChartErrorBoundary from './ChartErrorBoundary';
import ChartStatsRow from './ChartStatsRow';
import ChartGrid from './ChartGrid';
import {
  CHART_CONSTANTS,
  MONTH_LABELS,
  CHART_THEME,
  VALIDATION,
} from '../../constants/chartConstants';

const { width: screenWidth } = Dimensions.get('window');

interface GiftContributionsChartProps {
  gifts: PastGiftGiven[];
  profileName?: string;
  year?: number;
  onSquareTap?: (dayData: DailyGiftData) => void;
  onAddGiftPress?: (date?: string) => void;
  className?: string;
}

// Error fallback component
const ChartErrorState: React.FC<{ message: string }> = ({ message }) => (
  <View className="p-4 rounded-xl border bg-card dark:bg-card-dark border-border dark:border-border-dark">
    <View className="items-center py-8">
      <Text className="mb-2 text-lg text-text-secondary dark:text-text-secondary-dark">
        ⚠️
      </Text>
      <Text className="text-sm text-center text-text-secondary dark:text-text-secondary-dark">
        {message}
      </Text>
    </View>
  </View>
);

// Validation helper functions
const validateProps = (props: GiftContributionsChartProps): string | null => {
  const { gifts, year } = props;

  if (!Array.isArray(gifts)) {
    return 'Gifts must be an array';
  }

  if (year !== undefined) {
    if (
      typeof year !== 'number' ||
      year < VALIDATION.MIN_YEAR ||
      year > VALIDATION.MAX_YEAR
    ) {
      return `Year must be between ${VALIDATION.MIN_YEAR} and ${VALIDATION.MAX_YEAR}`;
    }
  }

  // Validate individual gifts
  for (const gift of gifts) {
    // Check if date exists and is valid (can be Timestamp, Date, or null)
    if (gift.date !== null && gift.date !== undefined) {
      try {
        const dateObj = timestampToDate(gift.date);
        // Check if it's a valid date
        if (isNaN(dateObj.getTime())) {
          return `Gift contains invalid date: ${gift.item || 'Unknown item'}`;
        }
      } catch (error) {
        return `Gift contains unparseable date: ${gift.item || 'Unknown item'}`;
      }
    }

    // Validate required fields
    if (!gift.item || typeof gift.item !== 'string') {
      return 'All gifts must have valid item names';
    }
  }

  return null;
};

const GiftContributionsChart: React.FC<GiftContributionsChartProps> = (
  props
) => {
  const {
    gifts,
    profileName,
    year = new Date().getFullYear(),
    onSquareTap,
    onAddGiftPress,
    className = '',
  } = props;

  // Validate props early
  const validationError = validateProps(props);
  if (validationError) {
    return <ChartErrorState message={validationError} />;
  }

  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [selectedDay, setSelectedDay] = useState<DailyGiftData | null>(null);
  const tooltipPositionRef = useRef<{ x: number; y: number } | null>(null);

  // Generate chart data from gifts
  const chartData: GiftContributionsData = useMemo(() => {
    try {
      return generateContributionsData(gifts, year);
    } catch (error) {
      console.warn('Error generating chart data:', error);
      return {
        year,
        weeks: [],
        totalGifts: 0,
        avgSuccessScore: 0,
        achievements: {
          currentStreak: 0,
          longestStreak: 0,
          totalGifts: 0,
          perfectWeeks: 0,
          bestMonth: '',
          badges: [],
        },
        stats: {
          thisMonth: 0,
          thisYear: 0,
          lastYearComparison: 0,
          avgGiftsPerMonth: 0,
          mostActiveDay: '',
          successRate: 0,
          favoriteCategory: '',
        },
      };
    }
  }, [gifts, year]);

  // Early return for empty data
  if (chartData.weeks.length === 0) {
    return (
      <View
        className={`p-4 rounded-xl border bg-card dark:bg-card-dark border-border dark:border-border-dark ${className}`}
      >
        <View className="items-center py-8">
          <Text className="mb-2 text-4xl">📊</Text>
          <Text className="mb-1 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
            No Gift Data Yet
          </Text>
          <Text className="text-sm text-center text-text-secondary dark:text-text-secondary-dark">
            Start tracking gifts to see your contribution chart
          </Text>
          {onAddGiftPress && (
            <TouchableOpacity
              onPress={() => onAddGiftPress()}
              className="px-4 py-2 mt-4 rounded-lg bg-primary dark:bg-primary-dark"
            >
              <Text className="font-medium text-white">
                Add Your First Gift
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  // Calculate responsive dimensions
  const squareSize = CHART_CONSTANTS.DEFAULT_SQUARE_SIZE;
  const squareSpacing = CHART_CONSTANTS.SQUARE_SPACING;

  // Get color for intensity level using theme system
  const getIntensityColor = useCallback(
    (intensity: number): string => {
      const colorScheme = isDark ? 'dark' : 'light';
      const colors = CHART_THEME.intensityColors[colorScheme];

      // Clamp intensity to valid range
      const clampedIntensity = Math.max(
        CHART_CONSTANTS.MIN_INTENSITY,
        Math.min(CHART_CONSTANTS.MAX_INTENSITY, intensity)
      );

      return colors[clampedIntensity] || colors[0];
    },
    [isDark]
  );

  // Handle square press
  const handleSquarePress = useCallback(
    (dayData: DailyGiftData, event: GestureResponderEvent) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Get touch position for tooltip
      const { pageX, pageY } = event.nativeEvent;
      tooltipPositionRef.current = { x: pageX, y: pageY };
      setSelectedDay(dayData);

      // Call external handler
      onSquareTap?.(dayData);
    },
    [onSquareTap]
  );

  // Handle tooltip close
  const handleTooltipClose = useCallback(() => {
    setSelectedDay(null);
    tooltipPositionRef.current = null;
  }, []);

  // Helper function to get achievement level
  const getAchievementLevel = useCallback((perfectWeeks: number): string => {
    if (perfectWeeks > CHART_CONSTANTS.GOLD_THRESHOLD) return 'Gold';
    if (perfectWeeks > CHART_CONSTANTS.SILVER_THRESHOLD) return 'Silver';
    return 'Bronze';
  }, []);

  // Render expanded view
  const renderExpandedView = () => {
    return (
      <Animated.View
        entering={SlideInRight.duration(CHART_CONSTANTS.SLIDE_IN_DURATION)}
        layout={Layout.springify()}
        className={`p-4 rounded-xl border bg-card dark:bg-card-dark border-border dark:border-border-dark ${className}`}
        accessible={true}
        accessibilityRole="image"
        accessibilityLabel={`Gift contributions chart for ${
          profileName || 'your profile'
        } in ${year}. ${chartData.totalGifts} total gifts given.`}
      >
        {/* Header */}
        <View className="flex-row justify-between items-center mb-4">
          <View className="flex-1">
            <Text className="text-lg font-bold text-text-primary dark:text-text-primary-dark">
              🎁 {profileName ? `${profileName}'s` : 'Your'} Gift Journey
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              See the gifts you've given
            </Text>
          </View>
        </View>

        {/* Stats row */}
        <ChartStatsRow chartData={chartData} />

        {/* Full chart */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ChartGrid
            chartData={chartData}
            squareSize={squareSize}
            squareSpacing={squareSpacing}
            getIntensityColor={getIntensityColor}
            onSquarePress={handleSquarePress}
          />
        </ScrollView>
      </Animated.View>
    );
  };

  return (
    <ChartErrorBoundary>
      <View>
        {renderExpandedView()}

        {/* Tooltip */}
        {selectedDay && tooltipPositionRef.current && (
          <ChartTooltip
            dayData={selectedDay}
            position={tooltipPositionRef.current}
            onClose={handleTooltipClose}
            onAddGift={() => {
              onAddGiftPress?.(selectedDay.date);
              handleTooltipClose();
            }}
          />
        )}
      </View>
    </ChartErrorBoundary>
  );
};

export default GiftContributionsChart;

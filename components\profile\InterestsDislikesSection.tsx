import React from 'react';
import { View, Text } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Feather } from '@expo/vector-icons';
import { TagInput } from '@/components/ui/TagInput';
import { interestSuggestions } from '@/constants/suggestions';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface InterestsDislikesSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
}

const InterestsDislikesSection: React.FC<InterestsDislikesSectionProps> = ({
  control,
  errors,
}) => {
  return (
    <View className="gap-4">
      {/* Interests */}
      <View>
        <View className="flex-row items-center mb-4">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-red-500/10">
            <Feather name="heart" size={20} color="#A3002B" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Interests & Hobbies
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              What they love and enjoy doing
            </Text>
          </View>
        </View>
        
        <View className="">
          <Controller
            control={control}
            name="interestsInput"
            render={({ field: { onChange, value } }) => (
              <TagInput
                tags={value ? value.split(',').map(i => i.trim()).filter(Boolean) : []}
                onChangeTags={(tags) => onChange(tags.join(', '))}
                placeholder="e.g., hiking, sci-fi movies, cooking, photography"
                error={errors.interestsInput?.message}
                accessibilityLabel="Interests input"
                suggestions={interestSuggestions}
              />
            )}
          />
        </View>
        
        {/* Helpful tip */}
        <View className="mb-8 bg-green-50 rounded-lg">
          <View className="flex-row items-center mb-2">
            <Feather name="gift" size={14} color="#DC2626" />
            <Text className="ml-2 text-xs font-medium">
              Gift Tip:
            </Text>
          </View>
          <Text className="text-xs text-green-600 dark:text-green-400">
            The more specific you are, the better! "Vintage vinyl records" is more helpful than just "music"
          </Text>
        </View>
      </View>

      {/* Dislikes */}
      <View>
        <View className="flex-row items-center mb-4">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-red-500/10">
            <Feather name="x-circle" size={20} color="#DC2626" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Dislikes & Avoid
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              Things to definitely avoid when gift shopping
            </Text>
          </View>
        </View>
        
        <View className="">
          <Controller
            control={control}
            name="dislikesInput"
            render={({ field: { onChange, value } }) => (
              <TagInput
                tags={value ? value.split(',').map(i => i.trim()).filter(Boolean) : []}
                onChangeTags={(tags) => onChange(tags.join(', '))}
                placeholder="e.g., spicy food, loud noises, clutter, bright colors"
                error={errors.dislikesInput?.message}
                accessibilityLabel="Dislikes input"
                suggestions={interestSuggestions}
              />
            )}
          />
        </View>
        
        {/* Helpful tip */}
        <View className="p-4 bg-red-50 rounded-lg">
          <View className="flex-row items-center mb-2">
            <Feather name="shield" size={14} color="#DC2626" />
            <Text className="ml-2 text-xs font-medium text-red-700 dark:text-red-300">
              Safety Net:
            </Text>
          </View>
          <Text className="text-xs text-red-600 dark:text-red-400">
            Include allergies, sensitivities, or style preferences they dislike to avoid gift mistakes
          </Text>
        </View>
      </View>
    </View>
  );
};

export default InterestsDislikesSection;
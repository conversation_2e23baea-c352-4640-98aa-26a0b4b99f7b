import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Dimensions,
} from 'react-native';
import { useLocalSearchPara<PERSON>, useRouter, Stack } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  SlideInRight, 
  SlideInUp, 
  SlideOutLeft,
  FadeInDown,
  Layout,
  withSpring,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Swipeable } from 'react-native-gesture-handler';
import { format, differenceInDays, isAfter, addYears } from 'date-fns';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, CustomDate } from '../../../../functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import EmptyState from '../../../../components/profile/EmptyState';
import AddCustomDateModal from '../../../../components/profile/AddCustomDateModal';
import FilteredEmptyState from '../../../../components/profile/FilteredEmptyState';
import { TIME_PERIODS } from '../../../../utils/dateUtils';

const { width } = Dimensions.get('window');

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface DateItem {
  id: string;
  name: string;
  type: 'basic' | 'custom';
  date: any;
  isBasic?: boolean;
  originalIndex?: number;
}

interface DatesStats {
  totalDates: number;
  basicDates: number;
  customDates: number;
  upcomingDates: number;
  oldestDate: Date | null;
  newestDate: Date | null;
}

type SortOption = 'upcoming' | 'chronological' | 'alphabetical' | 'type';
type FilterOption = 'all' | 'basic' | 'custom' | 'upcoming';

// Enhanced filter button labels
const FILTER_LABELS = {
  'all': 'All',
  'basic': 'Basic',
  'custom': 'Custom',
  'upcoming': `Next ${TIME_PERIODS.UPCOMING_DAYS} Days`,
} as const;

// Helper function to safely convert Firestore Timestamp or Date to Date
const toDate = (dateValue: any): Date | null => {
  if (!dateValue) return null;

  try {
    // Handle Firestore Timestamp
    if (dateValue.toDate && typeof dateValue.toDate === 'function') {
      const date = dateValue.toDate();
      return isValidDate(date) ? date : null;
    }

    // Handle other date formats
    const date = new Date(dateValue);
    return isValidDate(date) ? date : null;
  } catch (error) {
    console.warn('Invalid date value provided to toDate:', dateValue, error);
    return null;
  }
};

// Helper function to check if a date is valid
const isValidDate = (date: Date): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

// Helper to get next occurrence of a date this year or next year
const getNextOccurrence = (date: Date): Date => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const thisYear = new Date(currentYear, date.getMonth(), date.getDate());
  
  if (isAfter(thisYear, now)) {
    return thisYear;
  } else {
    return addYears(thisYear, 1);
  }
};

// Memoized Statistics Section Component
const StatisticsSection = memo(({ stats, isDark }: { stats: DatesStats; isDark: boolean }) => {
  const StatCard = memo(({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color,
    delay = 0 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
    delay?: number;
  }) => (
    <Animated.View
      entering={SlideInUp.delay(delay).duration(300)}
      className="flex-1 mx-1"
    >
      <Card className="items-center p-4">
        <View 
          className="justify-center items-center mb-2 w-12 h-12 rounded-full"
          style={{ backgroundColor: color + '20' }}
        >
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          {value}
        </Text>
        <Text className="text-sm font-medium text-center text-text-secondary dark:text-text-secondary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </Card>
    </Animated.View>
  ));

  return (
    <View className="mb-6">
      <View className="flex-row mb-4">
        <StatCard
          title="Total Dates"
          value={stats.totalDates}
          icon="calendar-multiple"
          color={isDark ? '#C70039' : '#A3002B'}
          delay={0}
        />
        <StatCard
          title="Upcoming"
          value={stats.upcomingDates}
          subtitle={`Next ${TIME_PERIODS.UPCOMING_DAYS} days`}
          icon="calendar-clock"
          color="#16A34A"
          delay={100}
        />
        <StatCard
          title="Custom Dates"
          value={stats.customDates}
          subtitle={`${stats.basicDates} basic`}
          icon="calendar-plus"
          color="#F59E0B"
          delay={200}
        />
      </View>
      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        <Card className="p-4 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
          <View className="flex-row items-center">
            <MaterialCommunityIcons
              name="calendar-heart"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              {stats.totalDates > 0 
                ? 'Keep track of special moments and never miss an important date'
                : 'Start building your calendar of special moments'
              }
            </Text>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
});

const DatesScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('upcoming');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [showAddDateModal, setShowAddDateModal] = useState(false);

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  // Header animation values
  const headerAddButtonScale = useSharedValue(1);

  const headerAddButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerAddButtonScale.value }],
      opacity: withSpring(headerAddButtonScale.value < 1 ? 0.7 : 1),
    };
  });

  const handleAddButtonPressIn = () => {
    headerAddButtonScale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };

  const handleAddButtonPressOut = () => {
    headerAddButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  // Memoized filter handlers to prevent unnecessary re-renders
  const handleFilterChange = useCallback((filter: FilterOption) => {
    setFilterBy(filter);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSortChange = useCallback((sort: SortOption) => {
    setSortBy(sort);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [showFilters]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleAddDate = useCallback(() => {
    setShowAddDateModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleCloseAddDateModal = useCallback(() => {
    setShowAddDateModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSaveDate = useCallback(async (dateData: Omit<CustomDate, 'id' | 'date'> & { date: Date | null; profileId: string | null }) => {
    if (!profile || !user?.uid) return;

    try {
      const newCustomDate: CustomDate = {
        id: `custom-${Date.now()}`,
        name: dateData.name,
        date: dateData.date ? Timestamp.fromDate(dateData.date) : null,
        customDateMonthDay: dateData.date ? `${String(dateData.date.getMonth() + 1).padStart(2, '0')}-${String(dateData.date.getDate()).padStart(2, '0')}` : undefined,
      };

      const updatedDates = [...(profile.customDates || []), newCustomDate];
      await updateSignificantOther(user.uid, id, {
        customDates: updatedDates,
      });

      setProfile(prev => prev ? { ...prev, customDates: updatedDates } : null);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err) {
      console.error('Error saving date:', err);
      Alert.alert('Error', 'Failed to save date. Please try again.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [profile, user?.uid, id]);

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load dates. Please try again.');
      console.error('Error fetching dates:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteDate = async (dateIndex: number) => {
    if (!profile || !user?.uid) return;

    const customDate = profile.customDates?.[dateIndex];
    if (!customDate) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      'Delete Date',
      `Are you sure you want to delete "${customDate.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const dateId = customDate.id || `temp-${dateIndex}`;
            setDeletingIds(prev => new Set(prev).add(dateId));

            try {
              const updatedDates = profile.customDates?.filter((_, index) => index !== dateIndex) || [];
              await updateSignificantOther(user.uid, id, {
                customDates: updatedDates,
              });

              setProfile(prev => prev ? { ...prev, customDates: updatedDates } : null);
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete date. Please try again.');
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(dateId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Prepare all dates with proper typing
  const allDatesData = useMemo((): DateItem[] => {
    const basicDates: DateItem[] = [];
    const customDates: DateItem[] = [];

    // Add basic dates
    if (profile?.birthday) {
      basicDates.push({
        id: 'birthday',
        name: 'Birthday',
        type: 'basic',
        date: profile.birthday,
        isBasic: true,
      });
    }

    if (profile?.anniversary) {
      basicDates.push({
        id: 'anniversary',
        name: 'Anniversary',
        type: 'basic',
        date: profile.anniversary,
        isBasic: true,
      });
    }

    // Add custom dates
    if (profile?.customDates) {
      profile.customDates.forEach((customDate, index) => {
        customDates.push({
          id: customDate.id || `custom-${index}`,
          name: customDate.name,
          type: 'custom',
          date: customDate.date,
          isBasic: false,
          originalIndex: index,
        });
      });
    }

    return [...basicDates, ...customDates];
  }, [profile?.birthday, profile?.anniversary, profile?.customDates]);

  // Statistics calculation
  const stats = useMemo((): DatesStats => {
    const totalDates = allDatesData.length;
    const basicDates = allDatesData.filter(d => d.type === 'basic').length;
    const customDates = allDatesData.filter(d => d.type === 'custom').length;
    
    // Upcoming dates using centralized constant
    const now = new Date();
    const upcomingDeadline = new Date();
    upcomingDeadline.setDate(now.getDate() + TIME_PERIODS.UPCOMING_DAYS);
    
    const upcomingDates = allDatesData.filter(dateItem => {
      const date = toDate(dateItem.date);
      if (!date) return false;
      
      const nextOccurrence = getNextOccurrence(date);
      return nextOccurrence >= now && nextOccurrence <= upcomingDeadline;
    }).length;

    // Oldest and newest dates
    const validDates = allDatesData
      .map(d => toDate(d.date))
      .filter((date): date is Date => date !== null);
    
    const oldestDate = validDates.length > 0 
      ? validDates.reduce((oldest, date) => date < oldest ? date : oldest, validDates[0])
      : null;
    
    const newestDate = validDates.length > 0 
      ? validDates.reduce((newest, date) => date > newest ? date : newest, validDates[0])
      : null;

    return {
      totalDates,
      basicDates,
      customDates,
      upcomingDates,
      oldestDate,
      newestDate,
    };
  }, [allDatesData]);

  // Filtered and sorted dates
  const filteredAndSortedDates = useMemo(() => {
    let dates = allDatesData;

    // Apply search filter
    if (searchQuery.trim()) {
      dates = dates.filter(dateItem => 
        dateItem.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply type filter
    if (filterBy !== 'all') {
      if (filterBy === 'basic') {
        dates = dates.filter(d => d.type === 'basic');
      } else if (filterBy === 'custom') {
        dates = dates.filter(d => d.type === 'custom');
      } else if (filterBy === 'upcoming') {
        const now = new Date();
        const upcomingDeadline = new Date();
        upcomingDeadline.setDate(now.getDate() + TIME_PERIODS.UPCOMING_DAYS);
        
        dates = dates.filter(dateItem => {
          const date = toDate(dateItem.date);
          if (!date) return false;
          
          const nextOccurrence = getNextOccurrence(date);
          return nextOccurrence >= now && nextOccurrence <= upcomingDeadline;
        });
      }
    }

    // Apply sorting
    dates = [...dates].sort((a, b) => {
      switch (sortBy) {
        case 'upcoming':
          const aDate = toDate(a.date);
          const bDate = toDate(b.date);
          if (!aDate && !bDate) return 0;
          if (!aDate) return 1;
          if (!bDate) return -1;
          
          const aNext = getNextOccurrence(aDate);
          const bNext = getNextOccurrence(bDate);
          return aNext.getTime() - bNext.getTime();
        
        case 'chronological':
          const aDateChron = toDate(a.date);
          const bDateChron = toDate(b.date);
          if (!aDateChron && !bDateChron) return 0;
          if (!aDateChron) return 1;
          if (!bDateChron) return -1;
          return aDateChron.getTime() - bDateChron.getTime();
        
        case 'alphabetical':
          return a.name.localeCompare(b.name);
        
        case 'type':
          if (a.type === 'basic' && b.type === 'custom') return -1;
          if (a.type === 'custom' && b.type === 'basic') return 1;
          return a.name.localeCompare(b.name);
        
        default:
          return 0;
      }
    });

    return dates;
  }, [allDatesData, searchQuery, filterBy, sortBy]);

  const FilterButton = memo(({ 
    label, 
    isActive, 
    onPress 
  }: { 
    label: string; 
    isActive: boolean; 
    onPress: () => void; 
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`
        px-4 py-2 rounded-full mr-2 border
        ${isActive 
          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
        }
      `}
      activeOpacity={0.7}
    >
      <Text className={`
        text-sm font-medium
        ${isActive 
          ? 'text-white' 
          : 'text-text-secondary dark:text-text-secondary-dark'
        }
      `}>
        {label}
      </Text>
    </TouchableOpacity>
  ));

  const renderRightActions = useCallback((dateItem: DateItem) => {
    if (dateItem.isBasic) return null; // Don't show delete for basic dates
    
    const isDeleting = deletingIds.has(dateItem.id);
    
    return (
      <View className="flex-row justify-center items-center mr-2 w-20 rounded-lg bg-error dark:bg-error-dark">
        <TouchableOpacity
          className="flex-1 justify-center items-center"
          onPress={() => dateItem.originalIndex !== undefined && handleDeleteDate(dateItem.originalIndex)}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <LoadingIndicator size="small" color="white" />
          ) : (
            <Feather name="trash-2" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
    );
  }, [deletingIds, handleDeleteDate]);

  const renderDateItem = useCallback(({ item, index }: { item: DateItem; index: number }) => {
    const isDeleting = deletingIds.has(item.id);
    const date = toDate(item.date);
    const canDelete = !item.isBasic;
    
    const dateDisplay = date ? (() => {
      const nextOccurrence = getNextOccurrence(date);
      const daysUntil = differenceInDays(nextOccurrence, new Date());
      const isUpcoming = daysUntil >= 0 && daysUntil <= 60;
      
      return {
        formatted: format(date, 'MMMM d, yyyy'),
        nextOccurrence: format(nextOccurrence, 'MMM d'),
        daysUntil,
        isUpcoming,
      };
    })() : null;

    const content = (
      <Card className="p-4">
        <View className="flex-row justify-between items-start">
          <View className="flex-1 mr-3">
            <View className="flex-row items-center mb-2">
              <View 
                className="p-2 mr-3 rounded-full" 
                style={{ 
                  backgroundColor: item.type === 'basic' ? '#A3002B20' : '#7C3AED20' 
                }}
              >
                                 <Feather 
                   name={item.type === 'basic' ? 'calendar' : 'plus-circle'} 
                   size={18} 
                   color={item.type === 'basic' ? '#A3002B' : '#7C3AED'} 
                 />
              </View>
              <View className="flex-1">
                <View className="flex-row items-center">
                  <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                    {item.name}
                  </Text>
                  {item.type === 'basic' && (
                    <View className="px-2 py-1 ml-2 rounded-full bg-accent/20 dark:bg-accent-dark/20">
                      <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                        Basic
                      </Text>
                    </View>
                  )}
                </View>
                {dateDisplay && (
                  <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                    {dateDisplay.formatted}
                  </Text>
                )}
              </View>
            </View>

            {dateDisplay?.isUpcoming && (
              <View className="ml-12">
                <View className="flex-row items-center px-3 py-2 bg-green-100 rounded-full dark:bg-green-900/30">
                  <MaterialCommunityIcons name="calendar-clock" size={14} color="#16A34A" />
                  <Text className="ml-2 text-xs font-medium text-green-700 dark:text-green-300">
                    {dateDisplay.daysUntil === 0 
                      ? 'Today!' 
                      : dateDisplay.daysUntil === 1 
                        ? 'Tomorrow' 
                        : `In ${dateDisplay.daysUntil} days`
                    }
                  </Text>
                </View>
              </View>
            )}
          </View>

          {canDelete && (
            <TouchableOpacity
              onPress={() => item.originalIndex !== undefined && handleDeleteDate(item.originalIndex)}
              disabled={isDeleting}
              className="p-2 rounded-full active:bg-error/10"
            >
              {isDeleting ? (
                <LoadingIndicator size="small" />
              ) : (
                <Feather name="more-horizontal" size={18} color="#9CA3AF" />
              )}
            </TouchableOpacity>
          )}
        </View>
      </Card>
    );

    if (canDelete) {
      return (
        <Swipeable
          renderRightActions={() => renderRightActions(item)}
          rightThreshold={40}
        >
          <Animated.View
            entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
            exiting={SlideOutLeft.duration(200)}
            layout={Layout.springify().damping(20)}
            className="mb-4"
          >
            {content}
          </Animated.View>
        </Swipeable>
      );
    }

    return (
      <Animated.View
        entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
        layout={Layout.springify().damping(20)}
        className="mb-4"
      >
        {content}
      </Animated.View>
    );
  }, [deletingIds, handleDeleteDate, renderRightActions]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading dates...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const dates = filteredAndSortedDates;

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: `${profile?.name || 'Profile'} - Dates`,
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: isDark ? '#111827' : '#F9FAFB' },
          headerShadowVisible: false,
          headerRight: () => (
            <AnimatedTouchableOpacity
              style={headerAddButtonAnimatedStyle}
              onPressIn={handleAddButtonPressIn}
              onPressOut={handleAddButtonPressOut}
              onPress={handleAddDate}
              className="flex-row justify-center items-center p-2 mr-1 rounded-full"
              accessibilityLabel="Add new date"
              accessibilityRole="button"
            >
              <Feather name="plus-circle" size={28} color="#A3002B" />
            </AnimatedTouchableOpacity>
          ),
        }}
      />
      <FlatList
        data={dates}
        renderItem={renderDateItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#A3002B']}
            tintColor={isDark ? '#C70039' : '#A3002B'}
          />
        }
        ListHeaderComponent={
          <View>
            {/* Header */}
            <Animated.View entering={FadeIn.duration(600)} className="mb-6">
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                Special occasions and memorable dates
              </Text>
            </Animated.View>

            {/* Statistics */}
            {allDatesData.length > 0 && (
              <StatisticsSection 
                stats={stats}
                isDark={isDark}
              />
            )}

            {/* Search and Filters */}
            {allDatesData.length > 0 && (
              <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-6">
                {/* Search Bar */}
                <View className="flex-row items-center mb-4">
                  <View className="flex-row flex-1 items-center px-4 py-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Feather name="search" size={20} color="#9CA3AF" />
                    <TextInput
                      placeholder="Search dates..."
                      placeholderTextColor="#9CA3AF"
                      value={searchQuery}
                      onChangeText={handleSearchChange}
                      className="flex-1 ml-3 text-text-primary dark:text-text-primary-dark"
                    />
                    {searchQuery.length > 0 && (
                      <TouchableOpacity onPress={clearSearch}>
                        <Feather name="x" size={20} color="#9CA3AF" />
                      </TouchableOpacity>
                    )}
                  </View>
                  <TouchableOpacity
                    onPress={toggleFilters}
                    className={`
                      ml-3 p-3 rounded-lg border
                      ${showFilters 
                        ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
                        : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }
                    `}
                  >
                    <Feather 
                      name="filter" 
                      size={20} 
                      color={showFilters ? 'white' : '#9CA3AF'} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Filter Options */}
                {showFilters && (
                  <Animated.View entering={FadeInDown.duration(200)} className="mb-4">
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Filter by:
                    </Text>
                    <View className="flex-row mb-3">
                      <FilterButton
                        label={FILTER_LABELS.all}
                        isActive={filterBy === 'all'}
                        onPress={() => handleFilterChange('all')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.basic}
                        isActive={filterBy === 'basic'}
                        onPress={() => handleFilterChange('basic')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.custom}
                        isActive={filterBy === 'custom'}
                        onPress={() => handleFilterChange('custom')}
                      />
                      <FilterButton
                        label={FILTER_LABELS.upcoming}
                        isActive={filterBy === 'upcoming'}
                        onPress={() => handleFilterChange('upcoming')}
                      />
                    </View>
                    
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Sort by:
                    </Text>
                    <View className="flex-row">
                      <FilterButton
                        label="Upcoming"
                        isActive={sortBy === 'upcoming'}
                        onPress={() => handleSortChange('upcoming')}
                      />
                      <FilterButton
                        label="Chronological"
                        isActive={sortBy === 'chronological'}
                        onPress={() => handleSortChange('chronological')}
                      />
                      <FilterButton
                        label="Alphabetical"
                        isActive={sortBy === 'alphabetical'}
                        onPress={() => handleSortChange('alphabetical')}
                      />
                      <FilterButton
                        label="Type"
                        isActive={sortBy === 'type'}
                        onPress={() => handleSortChange('type')}
                      />
                    </View>
                  </Animated.View>
                )}
              </Animated.View>
            )}

            {/* Results Summary */}
            {allDatesData.length > 0 && (
              <View className="mb-4">
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                  {dates.length === allDatesData.length 
                    ? `Showing all ${allDatesData.length} dates`
                    : `Showing ${dates.length} of ${allDatesData.length} dates`
                  }
                </Text>
              </View>
            )}
          </View>
        }
        ListEmptyComponent={
          // Show different empty states based on whether we have items but they're filtered out
          allDatesData.length > 0 && dates.length === 0 ? (
            <FilteredEmptyState
              filterType={filterBy as any}
              itemType="dates"
              totalCount={allDatesData.length}
              onAddAction={handleAddDate}
              onClearFilter={() => {
                setFilterBy('all');
                setSearchQuery('');
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              searchQuery={searchQuery || undefined}
            />
          ) : (
            <Animated.View entering={FadeIn.duration(600)} className="mt-8">
              <EmptyState
                icon="calendar"
                title="No Important Dates Yet"
                description="Start tracking special occasions and memorable dates to never miss an important moment with your loved one."
                actionText="Add First Date"
                onAction={handleAddDate}
                examples={[
                  "First date anniversary",
                  "Graduation day",
                  "Moving in together"
                ]}
                benefit="Keeping track of important dates helps you celebrate special moments and shows how much you care about your shared history together."
              />
            </Animated.View>
          )
        }
      />
      
      {/* Add Date Modal */}
      <AddCustomDateModal
        isVisible={showAddDateModal}
        onClose={handleCloseAddDateModal}
        onAddItem={handleSaveDate}
        profileId={id}
      />
    </SafeAreaView>
  );
};

export default DatesScreen;

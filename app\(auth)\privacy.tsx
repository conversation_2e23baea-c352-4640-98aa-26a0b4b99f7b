import React from 'react';
import { View, Text, ScrollView, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

export default function PrivacyScreen() {
  const router = useRouter();

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <View className="flex-row items-center px-6 pt-12 pb-4 bg-background border-b border-border">
        <Pressable onPress={handleBack} className="mr-4 p-2">
          <Ionicons name="arrow-back-outline" size={24} color="#7A3E4F" />
        </Pressable>
        <Text className="text-xl font-semibold text-text-primary">
          Privacy Policy
        </Text>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 px-6 py-6" showsVerticalScrollIndicator={false}>
        <Text className="mb-6 text-2xl font-bold text-text-primary">
          Giftmi Privacy Policy
        </Text>

        <Text className="mb-4 text-sm text-text-secondary">
          Last updated: {new Date().toLocaleDateString()}
        </Text>

        <View className="space-y-6">
          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              1. Information We Collect
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support. This may include your name, email address, and profile information.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              2. How We Use Your Information
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products, services, and events.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              3. Information Sharing
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information in certain limited circumstances, such as with service providers who assist us in operating our app.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              4. Data Security
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              5. Data Retention
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We retain your personal information for as long as necessary to provide you with our services and as described in this privacy policy. We may also retain and use your information as necessary to comply with legal obligations.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              6. Your Rights
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              You have the right to access, update, or delete your personal information. You may also have the right to restrict or object to certain processing of your information. To exercise these rights, please contact us.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              7. Children's Privacy
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              Our service is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              8. Changes to This Policy
            </Text>
            <Text className="mb-4 text-base leading-6 text-text-secondary">
              We may update this privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "Last updated" date.
            </Text>
          </View>

          <View>
            <Text className="mb-3 text-lg font-semibold text-text-primary">
              9. Contact Us
            </Text>
            <Text className="mb-8 text-base leading-6 text-text-secondary">
              If you have any questions about this privacy policy, please contact <NAME_EMAIL>.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

import React, { useState } from 'react';
import { View, Text, Pressable, Image, ScrollView } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeInDown
} from 'react-native-reanimated';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import { getErrorDisplayMessage } from '../../utils/authErrorHandling';
import { useOptimizedAnimations } from '../../hooks/useOptimizedAnimations';

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { resetPassword, isLoading } = useAuth();
  const router = useRouter();

  // Optimized animations for better performance
  const { headerAnimatedStyle, formAnimatedStyle, footerAnimatedStyle } = useOptimizedAnimations();

  const handleResetPassword = async () => {
    // Light haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setError(null);
    setSuccess(false);

    // Validation
    if (!email) {
      setError('Email address is required.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    // Enhanced email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    try {
      await resetPassword(email);
      setSuccess(true);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err: any) {
      console.error('Password reset failed:', err);
      const errorMessage = getErrorDisplayMessage(err.code, err.message);
      setError(errorMessage);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const handleBackToLogin = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.back();
  };

  return (
    <ScrollView 
      className="flex-1 bg-background"
      contentContainerStyle={{ flexGrow: 1 }}
      showsVerticalScrollIndicator={false}
    >
      <View className="flex-1 px-6 pt-12 pb-8">
        {/* Header Section */}
        <Animated.View 
          className="items-center mb-12"
          style={headerAnimatedStyle}
        >
          {/* Logo */}
          <View className="mb-6">
            <Image 
              source={require('@/assets/images/icon.png')}
              className="w-24 h-24"
              resizeMode="contain"
            />
          </View>
          
          {/* Title */}
          <Text className="mb-3 text-3xl font-bold text-center text-text-primary">
            Reset Password
          </Text>
          
          {/* Subtitle */}
          <Text className="px-4 text-lg font-medium text-center text-text-secondary">
            Enter your email to receive reset instructions
          </Text>
        </Animated.View>

        {/* Form Container */}
        <View className="flex-1 justify-center">
          <Animated.View 
            className="mx-auto w-full max-w-sm"
            style={formAnimatedStyle}
          >
            {!success ? (
              <>
                {/* Email Input */}
                <View className="mb-6">
                  <Input
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    editable={!isLoading}
                    leftIcon={
                      <Ionicons 
                        className='ml-2'
                        name="mail-outline" 
                        size={20} 
                        color="#7A3E4F" 
                      />
                    }
                  />
                </View>

                {/* Display error message */}
                {error && (
                  <Animated.View 
                    className="p-4 mb-6 bg-red-50 rounded-xl border border-red-200"
                    entering={FadeInDown.duration(300)}
                  >
                    <View className="flex-row items-center">
                      <Ionicons 
                        name="alert-circle-outline" 
                        size={20} 
                        color="#D90429" 
                        style={{ marginRight: 8 }}
                      />
                      <Text className="flex-1 text-sm text-feedback-error">
                        {error}
                      </Text>
                    </View>
                  </Animated.View>
                )}

                {/* Reset Button */}
                <Button
                  title="Send Reset Email"
                  onPress={handleResetPassword}
                  className="mb-6 rounded-xl" 
                  isLoading={isLoading} 
                  disabled={isLoading} 
                  variant="primary"
                />
              </>
            ) : (
              /* Success Message */
              <Animated.View 
                className="p-6 bg-green-50 rounded-xl border border-green-200"
                entering={FadeInDown.duration(300)}
              >
                <View className="items-center">
                  <Ionicons 
                    name="checkmark-circle-outline" 
                    size={48} 
                    color="#2D6A4F" 
                    style={{ marginBottom: 16 }}
                  />
                  <Text className="mb-2 text-lg font-semibold text-center text-green-800">
                    Email Sent!
                  </Text>
                  <Text className="mb-4 text-sm text-center text-green-700">
                    Check your email for password reset instructions. The link will expire in 1 hour.
                  </Text>
                  <Text className="text-xs text-center text-green-600">
                    Didn't receive the email? Check your spam folder or try again.
                  </Text>
                </View>
              </Animated.View>
            )}
          </Animated.View>
        </View>

        {/* Footer Links */}
        <Animated.View 
          className="pt-8 mt-auto"
          style={footerAnimatedStyle}
        >
          <Pressable 
            onPress={handleBackToLogin} 
            disabled={isLoading} 
            className="p-4"
          >
            <Text className="text-base text-center text-text-secondary">
              <Ionicons name="arrow-back-outline" size={16} color="#7A3E4F" />
              {' '}Back to Sign In
            </Text>
          </Pressable>

          {!success && (
            <Link href="/signup" asChild>
              <Pressable disabled={isLoading} className="p-4">
                <Text className="text-base text-center text-text-secondary">
                  Don't have an account?{' '}
                  <Text className="font-semibold underline text-primary-500">
                    Sign Up
                  </Text>
                </Text>
              </Pressable>
            </Link>
          )}
        </Animated.View>
      </View>
    </ScrollView>
  );
}

import React, { ReactNode } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Switch,
  Platform,
} from 'react-native'; // Import Switch & Platform
import { useRouter } from 'expo-router';
import { useAuth } from '../../../contexts/AuthContext';
import Button from '../../../components/ui/Button'; // Make sure this path is correct
import { MaterialCommunityIcons, Feather } from '@expo/vector-icons';
import { useThemeManager } from '../../../hooks/useThemeManager'; // Import the theme manager hook
import colors from 'tailwindcss/colors'; // Import tailwind colors for direct use if needed

// Type declarations
interface MenuItemProps {
  icon: ReactNode;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: ReactNode;
}

// Helper for user initials - No style changes needed
const getInitials = (name?: string | null, email?: string | null): string => {
  if (name && name.trim().length > 0) {
    const parts = name.trim().split(' ');
    if (parts.length === 1) return parts[0][0].toUpperCase();
    return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
  }
  if (email && email.length > 0) return email[0].toUpperCase();
  return '?';
};

// Card component with base styling according to the guide
const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = '',
}) => (
  // Added dark mode variants for background and border
  <View
    className={`overflow-hidden rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark ${className}`}
  >
    {children}
  </View>
);

const SettingsScreen: React.FC = () => {
  const { user, signOutUser, isLoading } = useAuth();
  const router = useRouter();
  // Use the theme manager hook & get colorScheme for conditional styling if needed
  const { userPreference, updateThemePreference, colorScheme } =
    useThemeManager();

  const displayName = user?.displayName || '';
  const email = user?.email || '';

  const handleSignOut = async (): Promise<void> => {
    try {
      await signOutUser();
      // Navigation handled by auth state
    } catch (error) {
      // Error handling is managed in context
    }
  };

  const navigateToProfiles = (): void => {
    router.push('/profiles');
  };

 
  // Reusable menu item component with updated styling
  const MenuItem: React.FC<MenuItemProps> = ({
    icon,
    title,
    subtitle,
    onPress,
    rightElement,
  }) => (
    <TouchableOpacity
      className={`flex-row items-center p-4 border-b border-border dark:border-border-dark last:border-b-0`}
      onPress={onPress}
      disabled={!onPress && !rightElement}
      accessibilityLabel={title}
      accessibilityRole={
        onPress ? 'button' : rightElement ? 'adjustable' : 'text'
      }
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View className="justify-center items-center w-10 h-10 rounded-full bg-background dark:bg-card-dark">
        {icon}
      </View>
      <View className="flex-1 ml-4">
        <Text className="text-base font-medium text-text-primary dark:text-text-primary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-sm text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </View>

      {rightElement ||
        (onPress && ( // Only show chevron if it's pressable and has no other right element
          <Feather name="chevron-right" size={20} color={"#A3002B"} />
        ))}
    </TouchableOpacity>
  );

  const handleMenuPress = (): void => {};

  const handleThemeToggle = (isOn: boolean) => {
    const newPreference = isOn ? 'dark' : 'light';
    updateThemePreference(newPreference);
  };

  const switchTrackColor = {
    false: colorScheme === 'dark' ? colors.gray[700] : colors.gray[300],
    true: colorScheme === 'dark' ? colors.gray[700] : colors.gray[500],
  };
  const switchThumbColor =
    Platform.OS === 'android'
      ? userPreference === 'dark'
        ? "#A3002B"
        : "#A3002B"
      : '#ffffff';

  return (
    <ScrollView className="flex-1 bg-background dark:bg-background-dark">
      <View className="px-6 pt-10 pb-6 shadow-sm bg-card dark:bg-card-dark">
        <View className="items-center">
          {user?.photoURL ? (
            <View className="overflow-hidden justify-center items-center mb-4 w-24 h-24 rounded-full border-2 shadow-sm bg-primary border-border dark:border-border-dark">
              <Text className="text-4xl font-bold text-white">
                {getInitials(displayName, email)}
              </Text>
            </View>
          ) : (
            <View className="justify-center items-center mb-4 w-24 h-24 rounded-full border-2 shadow-sm bg-primary border-border dark:border-border-dark">
              <Text className="text-4xl font-bold text-white">
                {getInitials(displayName, email)}
              </Text>
            </View>
          )}

          <Text
            className="text-2xl font-bold text-text-primary dark:text-text-primary-dark"
            accessibilityRole="header"
          >
            {displayName || 'Welcome'}
          </Text>
          {email && (
            <Text className="mt-1 text-base text-text-secondary dark:text-text-secondary-dark">
              {email}
            </Text>
          )}
        </View>
      </View>

      <View className="px-4 py-6">
        <Card className="mb-6">
          <View className="px-4 py-3 bg-gray-50 dark:bg-border-dark">
            <Text className="text-xs font-semibold tracking-wider uppercase text-text-secondary dark:text-text-secondary-dark">
              Account
            </Text>
          </View>

          <MenuItem
            icon={
              <MaterialCommunityIcons
                name="account-circle-outline"
                size={24}
                color={"#A3002B"}
              />
            }
            title="Personal Information"
            subtitle="Manage your personal details"
            onPress={() => router.push('/settings/personal-info')}
          />

          <MenuItem
            icon={
              <MaterialCommunityIcons
                name="bell-outline"
                size={22}
                color={"#A3002B"}
              />
            }
            title="Notifications"
            subtitle="Control your notification preferences"
            onPress={() => router.push('/settings/notifications')}
          />

          <MenuItem
            icon={<Feather name="shield" size={22} color={"#A3002B"} />}
            title="Privacy & Security"
            subtitle="Manage your account security settings"
            onPress={() => router.push('/settings/privacy-security')}
          />
        </Card>

        <Card className="mb-6">
          <View className="px-4 py-3 bg-gray-50 dark:bg-border-dark">
            <Text className="text-xs font-semibold tracking-wider uppercase text-text-secondary dark:text-text-secondary-dark">
              Profiles
            </Text>
          </View>

          <MenuItem
            icon={
              <MaterialCommunityIcons
                name="account-multiple-outline"
                size={24}
                color={"#A3002B"}
              />
            }
            title="Your Profiles"
            subtitle="Manage connected profiles"
            onPress={navigateToProfiles}
          />
        </Card>

        <Card className="mb-6">
          <View className="px-4 py-3 bg-gray-50 dark:bg-border-dark">
            <Text className="text-xs font-semibold tracking-wider uppercase text-text-secondary dark:text-text-secondary-dark">
              Preferences
            </Text>
          </View>

          <MenuItem
            icon={
              <MaterialCommunityIcons
                name="palette-outline"
                size={22}
                color={"#A3002B"}
              />
            }
            title="Appearance"
            subtitle={
              userPreference === 'dark'
                ? 'Dark mode enabled'
                : 'Light mode enabled'
            }
            rightElement={
              <Switch
                value={userPreference === 'dark'}
                onValueChange={handleThemeToggle}
                trackColor={switchTrackColor}
                thumbColor={switchThumbColor}
                ios_backgroundColor={switchTrackColor.false}
              />
            }
          />
        </Card>
        <Card className="mb-6">
          <View className="px-4 py-3 bg-gray-50 dark:bg-border-dark">
            <Text className="text-xs font-semibold tracking-wider uppercase text-text-secondary dark:text-text-secondary-dark">
              Support
            </Text>
          </View>

          <MenuItem
            icon={<Feather name="help-circle" size={22} color={"#A3002B"} />}
            title="Help & Support Center"
            subtitle="Find answers to common questions"
            onPress={() => router.push('./settings/help-support')}
          />

        
        </Card>
        <View className="mt-4 mb-4">
          <Button
            title={isLoading ? 'Signing Out...' : 'Sign Out'}
            onPress={handleSignOut}
            disabled={isLoading}
            isLoading={isLoading}
            className="px-5 py-3 w-full rounded-lg bg-error dark:bg-error-dark"
          />
        </View>
        <Text className="mb-6 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
          Version 1.0.0
        </Text>
      </View>
    </ScrollView>
  );
};

export default SettingsScreen;

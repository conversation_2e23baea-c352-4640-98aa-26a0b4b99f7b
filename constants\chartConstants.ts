// Chart configuration constants
export const CHART_CONSTANTS = {
  // Time periods
  WEEKS_PER_YEAR: 53,
  COMPACT_WEEKS: 26,
  DAYS_PER_WEEK: 7,
  MONTHS_PER_YEAR: 12,
  
  // Dimensions
  MIN_SQUARE_SIZE: 6,
  MAX_SQUARE_SIZE: 9,
  DEFAULT_SQUARE_SIZE: 10,
  SQUARE_SPACING: 3,
  CONTAINER_PADDING: 16,
  LABEL_SPACE: 60,
  
  // Chart spacing
  WEEK_MARGIN: 1,
  MONTH_LABEL_SPACING: 4.3, // Approximate weeks per month
  
  // Intensity levels
  INTENSITY_LEVELS: 5, // 0-4
  MIN_INTENSITY: 0,
  MAX_INTENSITY: 4,
  
  // Animation durations
  FADE_IN_DURATION: 400,
  SLIDE_IN_DURATION: 500,
  
  // Achievement thresholds
  GOLD_THRESHOLD: 10,
  SILVER_THRESHOLD: 5,
} as const;

// Month labels for chart display
export const MONTH_LABELS = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
] as const;

// Chart color theme configuration
export interface ChartTheme {
  intensityColors: {
    light: readonly [string, string, string, string, string];
    dark: readonly [string, string, string, string, string];
  };
}

export const CHART_THEME: ChartTheme = {
  intensityColors: {
    light: [
      '#ebebeb',  // 0: Light border color
      '#F4C2C2',  // 1: Very light accent
      '#E5355F',  // 2: Accent color
      '#C22A4F',  // 3: Darker accent
      '#A3002B',  // 4: Primary color
    ],
    dark: [
      '#ebebeb',  // 0: Light border color
      '#F4C2C2',  // 1: Very light accent
      '#E5355F',  // 2: Accent color
      '#C22A4F',  // 3: Darker accent
      '#A3002B',  // 4: Primary color
    ],
  },
} as const;

// Validation constants
export const VALIDATION = {
  MIN_YEAR: 2000,
  MAX_YEAR: new Date().getFullYear() + 10,
  MAX_GIFTS_PER_DAY: 50, // Reasonable upper limit
} as const; 
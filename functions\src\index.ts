import * as functions from "firebase-functions";
import {onSchedule, ScheduledEvent} from "firebase-functions/v2/scheduler";
import * as admin from "firebase-admin";
import {logger} from "firebase-functions";
import {GoogleGenerativeAI} from "@google/generative-ai";
import {SecretManagerServiceClient} from "@google-cloud/secret-manager";
import {v4 as uuidv4} from "uuid";
import {addDays, startOfDay, getMonth, getDate} from "date-fns";
import {
  // SignificantOtherProfile, // Keep original interface for reference if needed
  // CustomDate, // Removed unused import
  GeneralNote,
  significantOtherProfileSchema, // Import Zod schema
  SignificantOtherProfileValidated, // Import inferred type
  userProfileSchema, // Import User Profile Zod schema
  UserProfileValidated, // Import inferred User Profile type
} from "./types/firestore";
import * as fs from "fs"; // Import fs to read JSON
import * as path from "path"; // Import path for resolving file path
import {Expo, ExpoPushMessage, ExpoPushTicket} from "expo-server-sdk";

// Removed the basic isSignificantOtherProfile type guard

// --- Holiday Type ---
interface Holiday {
  name: string;
  month: number;
  day: number;
  type: string;
  description: string;
}

// Initialize Firebase Admin SDK (only once)
if (admin.apps.length === 0) {
  admin.initializeApp();
}
const db = admin.firestore();

// Initialize Expo client
const expo = new Expo();

// Define the structure for the expected request data from the client
interface RequestData {
  profileId?: string;
  occasion?: string;
  date?: string;
  budget?: string;
  query?: string; // Add optional query for generic search
}

// Define a basic structure for the SO profile data we expect from Firestore
// Adapt this based on your actual Firestore structure if needed
// Note: SOProfileForPrompt interface removed as it's no longer used
// after implementing Zod validation for user profiles.

// Define the structure for a gift recommendation returned by the AI
interface GiftRecommendation {
  recommendationId?: string; // Added by the function, optional initially
  name: string;
  description: string;
  priceRange: string;
  categories: string[];
  
  // Enhanced AI reasoning & confidence
  reasoning: {
    why: string;
    confidence: number; // 0-100
    personalizedMatch: string;
    profileConnections: string[];
  };
  
  // Gift characteristics
  attributes: {
    giftType: "physical" | "experiential" | "digital" | "subscription";
    thoughtfulness: "practical" | "sentimental" | "creative" | "luxurious";
    effort: "low" | "medium" | "high";
    surpriseFactor: "expected" | "delightful" | "unexpected";
  };
  
  // Emotional & social context
  context: {
    emotionalTone: string;
    anticipatedReaction: string;
    relationshipFit: string;
    conversationPotential: string;
  };
  
  // Presentation & practical
  practical: {
    presentationIdea: string;
    giftingTips: string;
    complementaryIdeas: string[];
    usageScenario: string;
  };
  
  // Visual hints for UI
  visual: {
    colorTheme: string;
    iconSuggestion: string;
    emoji: string;
    oneWordEssence: string;
  };
}

/**
 * Simple health check endpoint for monitoring services
 */
export const healthCheck = functions.https.onRequest((req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    service: "giftmi-functions"
  });
});

/**
 * HTTP Callable function to get gift recommendations for a specific profile
 * or a generic query. Uses Zod for profile data validation.
 */
export const getGiftRecommendations = functions.https.onCall(
  async (request: functions.https.CallableRequest<RequestData>) => {
    // Destructure data and auth from the request
    const {data, auth} = request;

    logger.info("getGiftRecommendations triggered", {structuredData: true});

    // 1. Authentication Check
    if (!auth) {
      logger.error("Authentication required.");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "The function must be called while authenticated."
      );
    }
    const userId = auth.uid;
    logger.info(`Authenticated user ID: ${userId}`);

    // 2. Request Data Validation
    const {profileId, occasion, date, query} = data;

    // Check if either profileId or query is provided
    if (!profileId && !query) {
      logger.error(
        "Invalid request data: either profileId or query must be provided.",
        data
      );
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Must provide either a valid \"profileId\" or a \"query\"."
      );
    }

    // If profileId is provided, validate it
    if (profileId && typeof profileId !== "string") {
      logger.error(
        "Invalid request data: profileId must be a string if provided.",
        data
      );
      throw new functions.https.HttpsError(
        "invalid-argument",
        "The function must be called with a valid \"profileId\" if provided."
      );
    }

    // If query is provided, validate it
    if (query && typeof query !== "string") {
      logger.error(
        "Invalid request data: query must be a string if provided.",
        data
      );
      throw new functions.https.HttpsError(
        "invalid-argument",
        "The function must be called with a valid \"query\" if provided."
      );
    }

    logger.info(
      "Requesting recommendations. Profile ID: " +
      `${profileId || "N/A"}, Query: ${query || "N/A"}`
    );

    // Log additional context if provided
    if (occasion) {
      logger.info(`Occasion context: ${occasion}`);
    }
    if (date) {
      logger.info(`Date context: ${date}`);
    }

    try { // Outer try block starts here
      // Use validated type for profile data when fetched
      let validatedProfileData: SignificantOtherProfileValidated | undefined;
      // Use validated type for user profile data when fetched
      let validatedUserProfileData: UserProfileValidated | undefined;
      let dislikeFeedback: Array<{
        recommendationId: string;
        name: string;
        description: string;
      }> = [];
      let likeFeedback: Array<{
        recommendationId: string;
        name: string;
        description: string;
      }> = [];
      let notesContent = ""; // Declare notesContent here

      // 3. Fetch Profile Data and Feedback
      if (profileId) {
        // Fetch data for the specified profile
        try {
          const profileDocRef = db.collection("significant_others")
            .doc(profileId);
          const profileDocSnap = await profileDocRef.get();

          if (!profileDocSnap.exists) {
            logger.error(`Profile document not found: ${profileId}`);
            throw new functions.https.HttpsError(
              "not-found",
              `Profile with ID ${profileId} not found.`
            );
          }

          // ** Zod Validation **
          const profileData = profileDocSnap.data();
          // Add profileId if missing (for backward compatibility)
          if (profileData && !profileData.profileId && profileId) {
            profileData.profileId = profileId;
          }
          const parseResult = significantOtherProfileSchema.safeParse(
            profileData
          );

          if (!parseResult.success) {
            logger.error(
              `Invalid profile data structure for profile ID: ${profileId}`,
              {zodError: parseResult.error.format()} // Log formatted Zod errors
            );
            throw new functions.https.HttpsError(
              "internal", // Or "data-loss" if appropriate
              `Profile data for ${profileId} is invalid.`
            );
          }
          // Use validated data from now on
          validatedProfileData = parseResult.data;

          // 4. Ownership Check (using validated data)
          if (validatedProfileData.userId !== userId) {
            logger.error(
              `User ${userId} attempted to access profile ${profileId} owned ` +
              `by ${validatedProfileData.userId}.`
            );
            throw new functions.https.HttpsError(
              "permission-denied",
              "You do not have permission to access this profile."
            );
          }

          logger.info(
            "Successfully fetched and validated profile data for: " +
            validatedProfileData.name
          );

          // Extract and format general notes (using validated data)
          notesContent = validatedProfileData.generalNotes &&
            validatedProfileData.generalNotes.length > 0 ?
            `General Notes: ${validatedProfileData.generalNotes
              .map((n: GeneralNote) => n.note).join("; ")}` :
            "";
          logger.debug("Notes Content:", notesContent);


          // 5. Fetch recent feedback for this profile
          try {
            // Fetch dislike feedback
            const dislikeQuery = db.collection("recommendation_feedback")
              .where("profileId", "==", profileId)
              .where("feedbackType", "==", "dislike")
              .orderBy("timestamp", "desc")
              .limit(10);
            const dislikeSnapshot = await dislikeQuery.get();
            dislikeFeedback = dislikeSnapshot.docs.map(
              (doc: admin.firestore.QueryDocumentSnapshot) => ({
                recommendationId: doc.data().recommendationId,
                name: doc.data().recommendationDetails?.name || "",
                description:
                  doc.data().recommendationDetails?.description || "",
              })
            );
            logger.info(
              `Fetched ${dislikeFeedback.length} recent dislike items`
            );
            logger.debug(
              "Dislike Feedback Contents:",
              JSON.stringify(dislikeFeedback)
            );

            // Fetch like feedback
            const likeQuery = db.collection("recommendation_feedback")
              .where("profileId", "==", profileId)
              .where("feedbackType", "==", "like")
              .orderBy("timestamp", "desc")
              .limit(10);
            const likeSnapshot = await likeQuery.get();
            likeFeedback = likeSnapshot.docs.map(
              (doc: admin.firestore.QueryDocumentSnapshot) => ({
                recommendationId: doc.data().recommendationId,
                name: doc.data().recommendationDetails?.name || "",
                description:
                  doc.data().recommendationDetails?.description || "",
              })
            );
            logger.info(
              `Fetched ${likeFeedback.length} recent like feedback items`
            );
            logger.debug(
              "Like Feedback Contents:",
              JSON.stringify(likeFeedback)
            );
          } catch (feedbackError) {
            logger.error("Error fetching feedback:", feedbackError);
            // Continue with recommendations even if feedback fetch fails
          }
        } catch (error) {
          logger.error("Error fetching profile data:", error);
          // If profile fetch fails, re-throw the error
          throw error;
        }
      } else if (query) {
        // Fetch data for the current user's profile (if one exists)
        // Note: User profile structure might differ, using UserProfileValidated
        try {
          const userProfileDocRef = db.collection("user_profiles").doc(userId);
          const userProfileDocSnap = await userProfileDocRef.get();

          if (userProfileDocSnap.exists) {
            // ** Zod Validation for User Profile **
            const userParseResult = userProfileSchema.safeParse(
              userProfileDocSnap.data()
            );

            if (userParseResult.success) {
              validatedUserProfileData = userParseResult.data;
              logger.info(
                "Successfully fetched and validated user profile data for: " +
                userId
              );
            } else {
              // Log validation error but continue without user profile context
              logger.warn(
                `Invalid user profile data structure for user: ${userId}. ` +
                "Proceeding with generic query only.",
                {zodError: userParseResult.error.format()}
              );
            }
          } else {
            logger.info(`User profile document not found for user: ${userId}`);
          }
        } catch (error) {
          logger.error("Error fetching user profile data:", error);
          // Continue even if user profile fetch fails
        }
      }


      // 6. Initialize Google AI SDK with Secret Manager
      const secretClient = new SecretManagerServiceClient();
      const projectId = process.env.GCLOUD_PROJECT;
      if (!projectId) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "GCLOUD_PROJECT environment variable not set"
        );
      }

      const [version] = await secretClient.accessSecretVersion({
        name: `projects/${projectId}/secrets/google-ai-key/versions/latest`,
      });

      if (!version.payload?.data) {
        throw new functions.https.HttpsError(
          "failed-precondition",
          "Google AI API key not configured in Secret Manager"
        );
      }

      const apiKey = version.payload.data.toString();
      const genAI = new GoogleGenerativeAI(apiKey);
      // Try gemini-pro if flash-lite has issues
      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash-lite",
        generationConfig: {
          responseMimeType: "application/json",
        },
      });

      // 7. Construct Prompt based on available data
      let prompt: string;
      // Use validated data if profileId was provided, otherwise validated
      // user profile data.
      const dataForPrompt = profileId ?
        validatedProfileData : validatedUserProfileData;

      if (dataForPrompt && profileId) { // Check profileId for this prompt path
        // Prompt for personalized recommendations using validated data
        prompt = [
          "You are an expert gift recommendation assistant. Your task is to",
          "suggest thoughtful, personalized gift ideas.",
          "",
          `I need gift recommendations for ${dataForPrompt.name}, my`,
          `${dataForPrompt.relationship || "significant other"}.`,
          // Include search query if provided
          query ? `The user is specifically looking for: "${query}"` : "",
          query ? "Please focus your recommendations around this search " +
            "query while considering their personal preferences below." : "",
          "",
          "Here are the details about them:",
          "- Interests: " + (dataForPrompt.interests?.join(", ") || "None"),
          "- Dislikes: " + (dataForPrompt.dislikes?.join(", ") || "None"),
          // Add preferences, including budget if available
          dataForPrompt.preferences ?
            "- Preferences: " + JSON.stringify(dataForPrompt.preferences) : "",
          // Construct budget string
          (() => {
            const min = dataForPrompt.preferences?.budgetMin;
            const max = dataForPrompt.preferences?.budgetMax;
            if (min !== undefined && max !== undefined) {
              return `- Budget: $${min} - $${max}`;
            } else if (min !== undefined) {
              return `- Budget: Around $${min}`;
            } else if (max !== undefined) {
              return `- Budget: Up to $${max}`;
            }
            return ""; // No budget info
          })(),
          dataForPrompt.sizes ?
            "- Sizes: " + JSON.stringify(dataForPrompt.sizes) : "",
          // Use validated nested arrays safely, add extra check for TS
          dataForPrompt.wishlistItems &&
          Array.isArray(dataForPrompt.wishlistItems) && // Explicit check
          dataForPrompt.wishlistItems.length > 0 ?
            "- Wishlist items: " + dataForPrompt.wishlistItems
              .map((w: { item: string }) => w.item).join(", ") : "", // Add type
          dataForPrompt.pastGiftsGiven &&
          Array.isArray(dataForPrompt.pastGiftsGiven) && // Explicit check
          dataForPrompt.pastGiftsGiven.length > 0 ?
            "- Past gifts given: " + dataForPrompt.pastGiftsGiven
              // Add type check to satisfy TS, though Zod should ensure objects
              .map((g) => typeof g === "object" && g !== null ? g.item : "")
              .filter(Boolean) // Remove empty strings if item was missing
              .join(", ") : "",
          // Add the general notes (already formatted in notesContent)
          notesContent ? "These are user provided notes about the person" : "",
          notesContent,
          // Add the calendar event context
          data.occasion ? "- Occasion: " + data.occasion : "",
          data.date ? "- Date: " + data.date : "",
          "",
          // Add specific guidance based on occasion type
          data.occasion === "Birthday" ?
            "This is for their birthday celebration. Please suggest unique," +
            " personal, and memorable birthday gifts that align with their" +
            " interests. Consider birthday gift traditions if applicable." : "",
          data.occasion === "Anniversary" ?
            "This is for our anniversary celebration. Suggest romantic," +
            " meaningful, or symbolic gifts that celebrate our relationship." +
            " Consider traditional anniversary gift themes if applicable." : "",
          // If it's a holiday from the calendar
          (data.occasion &&
            !["Birthday", "Anniversary", "Custom Date"]
              .includes(data.occasion)) ?
            "This is for the " + data.occasion + " holiday. Please suggest" +
            " appropriate, seasonal, and thoughtful gifts that would be" +
            " suitable for this specific occasion." : "",
          "",
          // Additional guidance for better results
          "Please consider the following in your recommendations:",
          query ? "1. Query Relevancy: ALL recommendations must be directly " +
            `related to "${query}" while incorporating their personal taste` :
            "1. Personalization: Suggest gifts that reflect their specific" +
            " interests and preferences",
          query ? "2. Personalization: Blend the search query with their " +
            "specific interests and preferences" :
            "2. Variety: Include a mix of practical, experiential, and" +
            " sentimental gift options",
          query ? "3. Creative Fusion: Find creative ways to combine the " +
            "search topic with their personality" :
            "3. Thoughtfulness: Focus on meaningful gifts rather than" +
            " generic options",
          "4. Variety: Include a mix of practical, experiential, and",
          " sentimental gift options",
          "5. Uniqueness: Suggest creative or uncommon gift ideas they",
          " might not expect",
          "6. Price Range: Provide gifts across different price points",
          "7. Avoid suggesting very similar gift ideas (e.g., two different",
          " types of sapphire jewelry). Aim for distinct suggestions.",
          "",
          // Feedback context for better recommendations
          ...(likeFeedback.length > 0 ? [
            "---",
            "For context, the user has previously liked items such as:",
            ...likeFeedback.map((feedback) =>
              `- ${feedback.name}: ${feedback.description}`
            ),
            "---",
            "",
          ] : []),
          ...(dislikeFeedback.length > 0 ? [
            "---",
            "IMPORTANT: Avoid suggesting gifts similar to",
            "the following items the user has previously disliked:",
            ...dislikeFeedback.map((feedback) =>
              `- ${feedback.name}: ${feedback.description}`
            ),
            "---",
            "",
          ] : []),
          "Provide exactly 5 gift recommendations as a JSON array with no",
          "additional text.",
          "Each recommendation should have these properties:",
          "- name: A concise title for the gift",
          "- description: A detailed, personalized explanation of why this",
          " gift is suitable",
          "- priceRange: Approximate cost range (e.g. '$20-50')",
          "- categories: Array of relevant categories (e.g. [\"Electronics\",",
          " \"Hobby\"])",
          "- reasoning: Object with:",
          "  - why: Brief explanation of why this gift fits",
          "  - confidence: Number 0-100 of how confident you are",
          "  - personalizedMatch: How this matches their specific profile",
          "  - profileConnections: Array of profile elements this connects to",
          "- attributes: Object with:",
          "  - giftType: 'physical', 'experiential', 'digital', or 'subscription'",
          "  - thoughtfulness: 'practical', 'sentimental', 'creative', or 'luxurious'",
          "  - effort: 'low', 'medium', or 'high'",
          "  - surpriseFactor: 'expected', 'delightful', or 'unexpected'",
          "- context: Object with:",
          "  - emotionalTone: The emotional feeling this gift evokes",
          "  - anticipatedReaction: How you think they'll react",
          "  - relationshipFit: How this fits your relationship level",
          "  - conversationPotential: Discussion opportunities this creates",
          "- practical: Object with:",
          "  - presentationIdea: How to present/wrap this gift",
          "  - giftingTips: Tips for giving this gift",
          "  - complementaryIdeas: Array of items that go well with this",
          "  - usageScenario: When/how they'd use this gift",
          "- visual: Object with:",
          "  - colorTheme: Colors associated with this gift",
          "  - iconSuggestion: Suggested icon name (feather icons)",
          "  - emoji: Single emoji that represents this gift",
          "  - oneWordEssence: Single word that captures the essence",
          "Return ONLY the JSON array with no additional text or formatting.",
        ].filter(Boolean).join("\n");
      } else if (query) {
        // Prompt for generic recommendations based on query and user's profile
        prompt = [
          "You are an expert gift recommendation assistant. Your task is to",
          "suggest thoughtful gift ideas based on a user's search query.",
          "",
          `I need gift recommendations based on following query: "${query}".`,
          "",
          // Include user's general preferences if available
          // (using dataForPrompt: UserProfileValidated | undefined here)
          dataForPrompt ? [
            "Consider the following general preferences of the user:",
            "- Interests: " + (dataForPrompt.interests?.join(", ") || "None"),
            "- Dislikes: " + (dataForPrompt.dislikes?.join(", ") || "None"),
            dataForPrompt.preferences ?
              "- Preferences: " + JSON.stringify(dataForPrompt.preferences) :
              "",
            // Safely access pastGiftsGiven only if dataForPrompt exists
            // and the property exists on it. Check length before mapping.
            dataForPrompt.pastGiftsGiven &&
            Array.isArray(dataForPrompt.pastGiftsGiven) && // Check is array
            dataForPrompt.pastGiftsGiven.length > 0 ?
              "- Past gifts given: " + dataForPrompt.pastGiftsGiven
                // Schema expects string[], no need for complex check now
                .join(", ") : "",
            "",
          ].filter(Boolean).join("\n") : "",
          "",
          "Please consider the following in your recommendations:",
          "1. Relevance: Suggest gifts that directly relate to the query.",
          "2. Variety: Include a mix of practical, experiential, and",
          " sentimental gift options.",
          "3. Thoughtfulness: Focus on meaningful gifts.",
          "4. Uniqueness: Suggest creative or uncommon gift ideas.",
          "5. Price Range: Provide gifts across different price points.",
          "",
          "Provide exactly 5 gift recommendations as a JSON array with no",
          "additional text.",
          "Each recommendation should have these properties:",
          "- name: A concise title for the gift",
          "- description: A detailed explanation of why this gift is suitable",
          " based on the query",
          "- priceRange: Approximate cost range (e.g. '$20-50')",
          "- categories: Array of relevant categories (e.g. [\"Electronics\",",
          " \"Hobby\"])",
          "- reasoning: Object with:",
          "  - why: Brief explanation of why this gift fits the query",
          "  - confidence: Number 0-100 of how confident you are",
          "  - personalizedMatch: How this matches the query context",
          "  - profileConnections: Array of query elements this connects to",
          "- attributes: Object with:",
          "  - giftType: 'physical', 'experiential', 'digital', or 'subscription'",
          "  - thoughtfulness: 'practical', 'sentimental', 'creative', or 'luxurious'",
          "  - effort: 'low', 'medium', or 'high'",
          "  - surpriseFactor: 'expected', 'delightful', or 'unexpected'",
          "- context: Object with:",
          "  - emotionalTone: The emotional feeling this gift evokes",
          "  - anticipatedReaction: How you think they'll react",
          "  - relationshipFit: How this fits general gift-giving",
          "  - conversationPotential: Discussion opportunities this creates",
          "- practical: Object with:",
          "  - presentationIdea: How to present/wrap this gift",
          "  - giftingTips: Tips for giving this gift",
          "  - complementaryIdeas: Array of items that go well with this",
          "  - usageScenario: When/how they'd use this gift",
          "- visual: Object with:",
          "  - colorTheme: Colors associated with this gift",
          "  - iconSuggestion: Suggested icon name (feather icons)",
          "  - emoji: Single emoji that represents this gift",
          "  - oneWordEssence: Single word that captures the essence",
          "Return ONLY the JSON array with no additional text or formatting.",
        ].filter(Boolean).join("\n");
      } else {
        // This case should ideally be caught by the initial validation,
        // but as a fallback, throw an error.
        logger.error("Neither profileId nor query provided after validation.");
        throw new functions.https.HttpsError(
          "internal",
          "Invalid request data after initial validation."
        );
      }

      logger.info("Full Prompt Being Sent to LLM:", prompt);
      try { // Inner try block for AI call and parsing
        // 7. Call Google AI (Gemini) API
        const result = await model.generateContent(prompt);
        const response = await result.response;
        let text = response.text();
        logger.debug("Raw LLM Response:", text);
        // 8. Clean and Parse Response
        let recommendations: GiftRecommendation[]; // Use defined type
        try { // Try block for JSON parsing
          // Remove markdown code blocks if present
          text = text.replace(/```json|```/g, "").trim();
          // Parse JSON
          recommendations = JSON.parse(text);
          // Validate response format
          if (!Array.isArray(recommendations) ||
              !recommendations.every((item) =>
                item && // Check if item itself is truthy
                item.name &&
                item.description &&
                item.priceRange &&
                item.categories &&
                Array.isArray(item.categories) && // Ensure categories is an array
                item.reasoning &&
                item.reasoning.why &&
                typeof item.reasoning.confidence === "number" &&
                item.reasoning.personalizedMatch &&
                Array.isArray(item.reasoning.profileConnections) &&
                item.attributes &&
                item.attributes.giftType &&
                item.attributes.thoughtfulness &&
                item.attributes.effort &&
                item.attributes.surpriseFactor &&
                item.context &&
                item.context.emotionalTone &&
                item.context.anticipatedReaction &&
                item.context.relationshipFit &&
                item.context.conversationPotential &&
                item.practical &&
                item.practical.presentationIdea &&
                item.practical.giftingTips &&
                Array.isArray(item.practical.complementaryIdeas) &&
                item.practical.usageScenario &&
                item.visual &&
                item.visual.colorTheme &&
                item.visual.iconSuggestion &&
                item.visual.emoji &&
                item.visual.oneWordEssence
              )) {
            throw new Error("Invalid response format from AI model");
          }
        } catch (parseError) { // Catch for JSON parsing errors
          logger.error(
            "Failed to parse AI response:",
            parseError,
            {response: text}
          );
          throw new functions.https.HttpsError(
            "internal",
            "Failed to parse AI response. Please try again."
          );
        }

        // 9. Return Formatted Recommendations
        // Add unique IDs to each recommendation
        const recommendationsWithIds: GiftRecommendation[] =
          recommendations.map(
            (rec: GiftRecommendation) => ({ // Use specific type
              ...rec,
              recommendationId: uuidv4(),
            })
          );

        return {
          success: true,
          recommendations: recommendationsWithIds,
        };
      } catch (apiError) { // Catch for AI API errors
        logger.error("Google AI API error:", apiError);
        throw new functions.https.HttpsError(
          "unavailable",
          "Failed to get recommendations from AI service"
        );
      }
    } catch (error: unknown) { // Outer catch block for main logic errors
      logger.error("Error processing getGiftRecommendations request:", error);
      if (error instanceof functions.https.HttpsError) {
        // Re-throw HttpsErrors directly
        throw error;
      } else if (error instanceof Error) {
        // Throw a generic internal error for other cases
        throw new functions.https.HttpsError(
          "internal",
          "An unexpected error occurred.",
          error.message
        );
      } else {
        // Handle non-Error objects being thrown
        throw new functions.https.HttpsError(
          "internal",
          "An unexpected non-error object was thrown."
        );
      }
    }
  }
);

// Interface for profile creation data (excluding server-managed fields)
interface CreateProfileData {
  name: string;
  relationship: string;
  birthday?: admin.firestore.Timestamp | null;
  anniversary?: admin.firestore.Timestamp | null;
  birthdayMonthDay?: string;
  anniversaryMonthDay?: string;
  interests?: string[];
  dislikes?: string[];
  preferences?: {
    favoriteColor?: string | null;
    preferredStyle?: string | null;
    favoriteBrands?: string[];
    budgetMin?: number;
    budgetMax?: number;
    [key: string]: any;
  };
  sizes?: {
    clothing?: string | null;
    shoe?: string | null;
    [key: string]: any;
  };
  wishlistItems?: Array<{
    item: string;
    link?: string;
    notes?: string;
    price?: number;
    priority?: "low" | "medium" | "high";
    isPurchased?: boolean;
    dateAdded?: admin.firestore.Timestamp | null;
  }>;
  pastGiftsGiven?: Array<{
    item: string;
    occasion?: string;
    date?: admin.firestore.Timestamp | null;
    reaction?: string;
  }>;
  generalNotes?: Array<{
    note: string;
    date?: admin.firestore.Timestamp | null;
  }>;
  customDates?: Array<{
    id: string;
    name: string;
    date?: admin.firestore.Timestamp | null;
    customDateMonthDay?: string;
  }>;
}

/**
 * Secure HTTP Callable function to create a new significant other profile.
 * The userId is derived from the authenticated user context (server-side) 
 * instead of being provided by the client, preventing IDOR attacks.
 */
export const createSignificantOther = functions.https.onCall(
  async (request: functions.https.CallableRequest<CreateProfileData>) => {
    // 1. Authentication Check
    const {auth, data} = request;
    if (!auth) {
      logger.error("User not authenticated for createSignificantOther.");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated to create a profile."
      );
    }
    
    // SECURITY: Derive userId from authenticated context (server-side)
    // This prevents IDOR attacks where clients could provide another user's ID
    const userId = auth.uid;
    logger.info(`Creating profile for authenticated user: ${userId}`);

    // 2. Input Validation
    if (!data || !data.name || !data.relationship) {
      logger.error("Invalid profile data: name and relationship are required.");
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Profile name and relationship are required."
      );
    }

    try {
      // 3. Create Profile Document
      logger.info(`Adding profile for user: ${userId}`, {
        profileName: data.name,
        relationship: data.relationship
      });

      const profileRef = db.collection("significant_others").doc();
      const profileId = profileRef.id;

      // Construct the profile document with server-managed fields
      const profileDocument = {
        // SECURITY: Server-assigned fields (not client-provided)
        userId: userId,
        profileId: profileId,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        
        // Client-provided data (validated)
        name: data.name.trim(),
        relationship: data.relationship.trim(),
        birthday: data.birthday || null,
        anniversary: data.anniversary || null,
        birthdayMonthDay: data.birthdayMonthDay || undefined,
        anniversaryMonthDay: data.anniversaryMonthDay || undefined,
        
        // Initialize arrays/objects with provided data or defaults
        interests: data.interests || [],
        dislikes: data.dislikes || [],
        preferences: data.preferences || {},
        sizes: data.sizes || {},
        wishlistItems: data.wishlistItems || [],
        pastGiftsGiven: data.pastGiftsGiven || [],
        generalNotes: data.generalNotes || [],
        customDates: data.customDates || [],
      };

      // Save to Firestore
      await profileRef.set(profileDocument);

      logger.info(`Profile created successfully with ID: ${profileId}`, {
        userId: userId,
        profileId: profileId,
        profileName: data.name
      });

      // Return the new profile ID
      return {
        profileId: profileId,
        success: true,
        message: "Profile created successfully"
      };

    } catch (error) {
      logger.error("Error creating significant other profile:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Failed to create profile. Please try again."
      );
    }
  }
);

/**
 * HTTP Callable function to delete all Firestore data associated with the
 * requesting user.
 */
export const deleteUserData = functions.https.onCall(
  async (request: functions.https.CallableRequest<unknown>) => {
    // 1. Authentication Check
    const {auth} = request;
    if (!auth) {
      logger.error("User not authenticated for deleteUserData.");
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated to delete data."
      );
    }
    const uid = auth.uid;
    logger.info(`Attempting to delete data for user: ${uid}`);

    // 2. Firestore Deletion Logic
    try {
      // Identify collections/documents to delete
      // Primarily the significant_others collection for now
      const profilesQuery = db.collection("significant_others")
        .where("userId", "==", uid);

      // Get Documents
      const profilesSnapshot = await profilesQuery.get();

      // Batch Delete
      const batch = db.batch();
      let deletedProfileCount = 0;
      const profileIdsToDelete: string[] = []; // Store profile IDs

      profilesSnapshot.forEach((doc) => {
        batch.delete(doc.ref);
        profileIdsToDelete.push(doc.id); // Collect profile ID
        deletedProfileCount++;
      });

      // Also attempt to delete the user's profile document if it exists
      const userProfileRef = db.collection("user_profiles").doc(uid);
      const userProfileSnap = await userProfileRef.get();
      let userProfileDeleted = false;
      if (userProfileSnap.exists) {
        batch.delete(userProfileRef);
        userProfileDeleted = true;
        logger.info(`Adding user profile doc deletion to batch for: ${uid}`);
      }

      // Delete related recommendation_feedback
      let deletedFeedbackCount = 0;
      if (profileIdsToDelete.length > 0) {
        // Firestore 'in' query supports up to 30 elements per query.
        // If more profiles could exist, chunking would be needed.
        // Assuming < 30 profiles per user for now.
        const feedbackQuery = db.collection("recommendation_feedback")
          .where("profileId", "in", profileIdsToDelete);
        const feedbackSnapshot = await feedbackQuery.get();
        feedbackSnapshot.forEach((doc) => {
          batch.delete(doc.ref);
          deletedFeedbackCount++;
        });
        if (deletedFeedbackCount > 0) {
          logger.info(`Adding ${deletedFeedbackCount} feedback docs to batch.`);
        }
      }

      const needsCommit = deletedProfileCount > 0 ||
                          userProfileDeleted ||
                          deletedFeedbackCount > 0;

      if (needsCommit) {
        const profileLog = deletedProfileCount > 0 ?
          `${deletedProfileCount} profiles` : "";
        const userLog = userProfileDeleted ? "user profile" : "";
        const feedbackLog = deletedFeedbackCount > 0 ?
          `${deletedFeedbackCount} feedback` : "";
        // Construct log message carefully
        const parts = [profileLog, userLog, feedbackLog].filter(Boolean);
        logger.info(`Deleting ${parts.join(", ")} for user ${uid}.`);
        await batch.commit();
        logger.info(`Successfully deleted Firestore data for user: ${uid}`);
      } else {
        logger.info(`No data found to delete for user: ${uid}`); // Updated log
      }

      // 3. Return Success
      return {success: true, message: "User data deleted successfully."};
    } catch (error) {
      logger.error(`Error deleting Firestore data for user: ${uid}`, error);
      // Throwing HttpsError ensures the client gets a structured error
      throw new functions.https.HttpsError(
        "internal",
        "Failed to delete user data.",
        // Including the original error message might be helpful for debugging
        // but consider security implications if exposing too much detail.
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }
);

/**
 * Sends reminders for upcoming significant dates (birthdays, anniversaries,
 * custom dates) and holidays. Uses Zod for profile data validation.
 * - Uses MM-DD fields for profile dates.
 * - Reads holidays from utils/holidays.json.
 * - Validates profile data.
 * - Batches token fetches.
 */
export const sendEventReminders = onSchedule(
  {
    schedule: "every day 09:00", // Runs daily at 9 AM UTC
    timeZone: "UTC",
    region: "europe-west1",
    // Consider memory/timeout settings if processing many users/events
    // memory: "512MiB",
    // timeoutSeconds: 300,
  },
  async (context: ScheduledEvent) => {
    logger.info("Running sendEventReminders function", {context});

    const REMINDER_DAYS_AHEAD = 7; // Send reminders 7 days in advance

    try {
      // 1. Calculate Target Date (MM-DD format)
      const today = startOfDay(new Date());
      const reminderTargetDate = addDays(today, REMINDER_DAYS_AHEAD);
      // date-fns getMonth is 0-indexed, add 1 for calendar month
      const targetMonth = getMonth(reminderTargetDate) + 1;
      const targetDay = getDate(reminderTargetDate);
      const targetMonthDay =
        `${targetMonth.toString().padStart(2, "0")}-` + // Format MM-DD
        `${targetDay.toString().padStart(2, "0")}`;

      logger.info(
        `Checking for events and holidays on MM-DD: ${targetMonthDay}`
      );

      // --- Data Structures ---
      // Map<profileId, { profileData, eventType, eventName }>
      // Store VALIDATED profile data
      const profilesToNotify = new Map<string, {
        profile: SignificantOtherProfileValidated; // Use validated type
        eventType: string;
        eventName: string;
      }>();
      // Set of user IDs who have profiles (for holiday check)
      const usersWithProfiles = new Set<string>();
      // Set of user IDs needing any notification (profile or holiday)
      const allUserIdsToNotify = new Set<string>();
      // Map<userId, token[]>
      const userTokens = new Map<string, string[]>();
      // List of holidays happening on the target date
      let upcomingHolidays: Holiday[] = [];

      // --- 2. Check for Upcoming Holidays ---
      try {
        // Construct the absolute path to holidays.json relative to this file
        // holidays.json is now in the functions directory
        const holidaysPath = path.resolve(
          __dirname, "../holidays.json"
        );
        const holidaysData = fs.readFileSync(holidaysPath, "utf-8");
        const allHolidays: Holiday[] = JSON.parse(holidaysData);

        upcomingHolidays = allHolidays.filter((holiday) =>
          holiday.month === targetMonth && holiday.day === targetDay
        );

        if (upcomingHolidays.length > 0) {
          const holidayNames = upcomingHolidays.map((h) => h.name).join(", ");
          logger.info(`Found upcoming holidays: ${holidayNames}`);
          // If holidays are found, find all users who have profiles
          const allProfilesSnapshot = await db
            .collection("significant_others")
            .select("userId") // Only fetch userId to minimize reads
            .get();
          allProfilesSnapshot.forEach((doc) => {
            const data = doc.data();
            // Basic check for userId existence before adding
            if (data && typeof data.userId === "string") {
              usersWithProfiles.add(data.userId);
              allUserIdsToNotify.add(data.userId); // Add to overall list
            }
          });
          logger.info(
            `Found ${usersWithProfiles.size} users with profiles ` +
            "for holiday check."
          );
        } else {
          logger.info("No upcoming holidays found for the target date.");
        }
      } catch (holidayError) {
        logger.error("Error reading/processing holidays.json:", holidayError);
        // Continue without holiday notifications if file reading fails
      }

      // --- 3. Query Firestore for Profile-Specific Events ---
      const profileQueries = [
        db.collection("significant_others")
          .where("birthdayMonthDay", "==", targetMonthDay),
        db.collection("significant_others")
          .where("anniversaryMonthDay", "==", targetMonthDay),
        // Assuming customDateMonthDay stores MM-DD for the *next* occurrence
        db.collection("significant_others")
          .where("customDateMonthDay", "==", targetMonthDay),
      ];

      const queryResults = await Promise.allSettled(
        profileQueries.map((q) => q.get())
      );

      queryResults.forEach((result, index) => {
        if (result.status === "fulfilled") {
          const snapshot = result.value;
          snapshot.forEach((doc) => {
            const rawProfileData = doc.data();

            // **Runtime Validation using Zod**
            const parseResult = significantOtherProfileSchema.safeParse(
              rawProfileData
            );

            if (!parseResult.success) {
              logger.warn(
                "Invalid profile data structure found, " +
                `skipping doc ID: ${doc.id}`,
                // Break long line
                {zodError: parseResult.error.format()}
              );
              return; // Skip this document
            }
            // Use validated data from now on
            const validatedProfileData = parseResult.data;

            // Determine event type based on query index
            let eventType = "";
            let eventName = ""; // For custom dates
            if (index === 0) eventType = "Birthday";
            if (index === 1) eventType = "Anniversary";
            if (index === 2) {
              eventType = "Custom Date";
              // Attempt to find the specific custom date name using
              // validated data
              const customDateEntry = validatedProfileData.customDates?.find(
                (cd) => { // cd type inferred from schema
                  if (!cd?.date) return false;
                  // Zod schema ensures cd.date is Timestamp | null
                  const dateObj = cd.date?.toDate(); // Use optional chaining
                  if (!dateObj) return false;
                  const cdMonth = dateObj.getMonth() + 1;
                  const cdDay = dateObj.getDate();
                  return cdMonth === targetMonth && cdDay === targetDay;
                }
              );
              eventName = customDateEntry?.name || "Custom Event";
            }

            // Add to notification map if not already present for this profile
            // Use validatedProfileData
            const currentProfileId = validatedProfileData.profileId || doc.id;
            if (!profilesToNotify.has(currentProfileId)) {
              profilesToNotify.set(currentProfileId, {
                profile: validatedProfileData, // Store validated data
                eventType: eventType,
                eventName: eventName,
              });
              allUserIdsToNotify.add(validatedProfileData.userId); // Add user
              logger.info(
                `Found matching ${eventType} for profile: ` +
                currentProfileId
              );
            } else {
              // Profile already added (e.g., birthday), log additional event
              logger.info(
                `Profile ${currentProfileId} also has matching ` +
                `${eventType}.`
              );
            }
          });
        } else {
          logger.error(
            `Failed to execute profile query index ${index}:`, result.reason
          );
        }
      });

      if (allUserIdsToNotify.size === 0) {
        logger.info("No users require notifications today.");
        return; // Exit cleanly
      }

      // --- 4. Batch Fetch Push Tokens ---
      const userIdsArray = Array.from(allUserIdsToNotify);
      const MAX_IN_QUERIES = 30; // Firestore 'in' query limit

      for (let i = 0; i < userIdsArray.length; i += MAX_IN_QUERIES) {
        const userChunk = userIdsArray.slice(i, i + MAX_IN_QUERIES);
        if (userChunk.length === 0) continue;

        try {
          const tokensQuery = db.collection("pushTokens")
            .where("userId", "in", userChunk);
          const tokensSnapshot = await tokensQuery.get();

          tokensSnapshot.forEach((doc) => {
            const tokenData = doc.data();
            // Validate token data structure before using
            const userId = tokenData?.userId;
            const token = tokenData?.token;
            const isValidTokenData = userId && token &&
                                     typeof userId === "string" &&
                                     typeof token === "string";
            if (isValidTokenData) {
              const currentTokens = userTokens.get(userId) || [];
              currentTokens.push(token);
              userTokens.set(userId, currentTokens);
            } else {
              logger.warn(`Invalid token data found in doc: ${doc.id}`);
            }
          });
        } catch (tokenError) {
          logger.error(
            `Error fetching token chunk ${i / MAX_IN_QUERIES}:`, tokenError
          );
        }
      }
      logger.info(`Fetched tokens for ${userTokens.size} users.`);


      // --- 5. Send Notifications ---
      // Define a type for the successful resolution of notification promises
      type NotificationResultContext = {
        response: ExpoPushTicket[];
        tokens: string[];
        userId: string;
        type: string; // "Holiday", "Birthday", etc.
        profileId?: string; // Only for profile-specific notifications
      };
      // Use the specific type or null for the promise array
      const notificationPromises:
        Promise<NotificationResultContext | null>[] = [];


      // 5a. Send Holiday Notifications
      if (upcomingHolidays.length > 0) {
        for (const holiday of upcomingHolidays) {
          for (const userId of usersWithProfiles) { // Iterate users w/ profiles
            const tokens = userTokens.get(userId);
            if (tokens && tokens.length > 0) {
              const title = `Upcoming Holiday: ${holiday.name}`;
              const body = `${holiday.name} is in ${REMINDER_DAYS_AHEAD} days!`;
              
              // Create Expo push messages
              const messages: ExpoPushMessage[] = tokens.map((token) => ({
                to: token,
                sound: "default",
                title: title,
                body: body,
                data: {eventType: "Holiday", eventName: holiday.name},
              }));
              
              logger.info(
                `Queueing holiday notification for ${holiday.name} ` +
                `to user ${userId}`
              );
              notificationPromises.push(
                expo.sendPushNotificationsAsync(messages)
                  .then((response: ExpoPushTicket[]) => ({
                    response, tokens, userId, type: "Holiday",
                  }))
                  .catch((error: Error) => {
                    logger.error(
                      `Failed sending holiday notification to user ${userId}`,
                      error
                    );
                    return null; // Allow Promise.allSettled to continue
                  })
              );
            } else {
              logger.warn(
                `No tokens found for user ${userId} for holiday notification.`
              );
            }
          }
        }
      }

      // 5b. Send Profile-Specific Notifications
      for (const [profileId, data] of profilesToNotify.entries()) {
        const {profile, eventType, eventName} = data;
        const userId = profile.userId;
        const tokens = userTokens.get(userId);

        if (tokens && tokens.length > 0) {
          const title = `Upcoming ${eventType}!`;
          // Construct body carefully to fit line limit
          const eventDisplayName = eventName || eventType.toLowerCase();
          let body = `${profile.name}'s ${eventDisplayName} is in `;
          body += `${REMINDER_DAYS_AHEAD} days!`;

          // Create Expo push messages
          const messages: ExpoPushMessage[] = tokens.map((token) => ({
            to: token,
            sound: "default",
            title: title,
            body: body,
            data: {profileId, eventType, eventName},
          }));
          
          logger.info(
            `Queueing ${eventType} notification for profile ${profileId} ` +
            `to user ${userId}`
          );
          notificationPromises.push(
            expo.sendPushNotificationsAsync(messages)
              .then((response: ExpoPushTicket[]) => ({
                response, tokens, userId, type: eventType, profileId,
              }))
              .catch((error: Error) => {
                logger.error(
                  `Failed sending ${eventType} notification for profile ` +
                  `${profileId} to user ${userId}`, error
                );
                return null; // Allow Promise.allSettled to continue
              })
          );
        } else {
          logger.warn(
            `No tokens found for user ${userId} for profile ` +
            `${profileId} notification.`
          );
        }
      }

      // --- 6. Process Notification Results & Cleanup Tokens ---
      const results = await Promise.allSettled(notificationPromises);
      // Token removal logic remains complex without doc refs. Logging only.
      let totalSuccessCount = 0;
      let totalFailureCount = 0;

      results.forEach((result) => {
        if (result.status === "fulfilled" && result.value) {
          // Type assertion needed as catch returns null
          const notificationResult = result.value as NotificationResultContext;
          if (!notificationResult) return; // Skip if null from catch block

          const {response, tokens, userId} = notificationResult;
          
          // Count successful and failed notifications
          const successCount = response.filter(ticket => ticket.status === "ok").length;
          const failureCount = response.filter(ticket => ticket.status === "error").length;
          
          totalSuccessCount += successCount;
          totalFailureCount += failureCount;

          if (failureCount > 0) {
            response.forEach(
              (ticket: ExpoPushTicket, index: number) => {
                if (ticket.status === "error") {
                  const failedToken = tokens[index] || "unknown";
                  logger.error(
                    `Failure sending notification to token: ${failedToken} ` +
                    `for user ${userId}`, ticket.message
                  );
                  // Check if token is invalid and needs removal
                  const isTokenInvalid = ticket.details && 
                    (ticket.details.error === "DeviceNotRegistered" ||
                     ticket.details.error === "InvalidCredentials");
                  if (isTokenInvalid) {
                    // Log token needing removal (manual/separate process)
                    logger.warn(
                      `Token needs removal: ${failedToken} for user ${userId}`
                    );
                    // Example of how deletion *would* work if we had the ref:
                    // const tokenRef = db.collection("pushTokens")
                    //  .where("token", "==", failedToken).limit(1);
                    // tokensToRemove.push(tokenRef.get().then(snap =>
                    //  snap.docs[0]?.ref.delete()));
                  }
                }
              } // End inner forEach callback
            ); // End response.forEach
          }
        } else if (result.status === "rejected") {
          // Error already logged in the individual catch blocks
          logger.error("Notification promise rejected:", result.reason);
        }
      });

      logger.info(
        `Notification sending complete. Success: ${totalSuccessCount}, ` +
        `Failure: ${totalFailureCount}`
      );

      logger.info("sendEventReminders function finished successfully.");
      return; // Return void for scheduler handler
    } catch (error) {
      logger.error("Critical error in sendEventReminders function:", error);
      // Ensure function completes even on top-level error for scheduler
      return; // Return void for scheduler handler
    }
  }
);

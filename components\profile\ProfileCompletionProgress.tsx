import React from 'react';
import { View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, useSharedValue, useAnimatedStyle, withSpring, interpolateColor } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { ProfileFormData } from './ProfileForm';

interface ProfileCompletionProgressProps {
  formData: Partial<ProfileFormData>;
  className?: string;
}

const ProfileCompletionProgress: React.FC<ProfileCompletionProgressProps> = ({ 
  formData, 
  className = '' 
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors for consistent styling
  const themedColors = {
    primary: isDark ? '#D96D00' : '#A3002B',
    accent: isDark ? '#0A84FF' : '#007AFF',
    background: isDark ? '#111827' : '#F9FAFB',
    card: isDark ? '#1F2937' : '#FFFFFF',
    border: isDark ? '#374151' : '#E5E7EB',
    textPrimary: isDark ? '#F9FAFB' : '#1F2937',
    textSecondary: isDark ? '#9CA3AF' : '#6B7280',
    textDisabled: isDark ? '#6B7280' : '#9CA3AF',
    error: isDark ? '#F87171' : '#A3002B',
    success: isDark ? '#4ADE80' : '#A3002B',
    warning: isDark ? '#FBBF24' : '#A3002B',
  };
  // Use the same calculation logic as in index.tsx
  const calculateProfileStrength = (data: Partial<ProfileFormData>): number => {
    if (!data) return 0;
    let strength = 0;
    const MAX_ITEMS_FOR_POINTS = 3;
    const POINTS_PER_ITEM_ARRAY = 4;
    const POINTS_PER_SIMPLE_FIELD = 5;
    const POINTS_PER_PREFERENCE = 7;

    // Basic fields
    if (data.birthday) strength += POINTS_PER_SIMPLE_FIELD;
    if (data.anniversary) strength += POINTS_PER_SIMPLE_FIELD;
    
    // Interests and dislikes (split comma-separated strings like the stored data)
    const interestsArray = data.interestsInput ? data.interestsInput.split(',').map(item => item.trim()).filter(Boolean) : [];
    const dislikesArray = data.dislikesInput ? data.dislikesInput.split(',').map(item => item.trim()).filter(Boolean) : [];
    
    strength += Math.min(interestsArray.length, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
    strength += Math.min(dislikesArray.length, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
    
    // Preferences
    if (data.preferences?.favoriteColor) strength += POINTS_PER_PREFERENCE;
    if (data.preferences?.preferredStyle) strength += POINTS_PER_PREFERENCE;
    
    // Budget points
    if (data.preferences?.budgetMin && data.preferences?.budgetMax)
      strength += POINTS_PER_PREFERENCE;
    else if (data.preferences?.budgetMin || data.preferences?.budgetMax)
      strength += Math.floor(POINTS_PER_PREFERENCE / 2);

    // Favorite brands
    strength += Math.min(
      data.preferences?.favoriteBrands?.length || 0,
      MAX_ITEMS_FOR_POINTS
    ) * POINTS_PER_ITEM_ARRAY;
    
    // Sizes (check direct fields from form data)
    const hasClothingSize = data.clothingSize;
    const hasShoeSize = data.shoeSize;
    if (hasClothingSize || hasShoeSize) strength += POINTS_PER_PREFERENCE;

    // Arrays
    strength += Math.min(data.wishlistItems?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
    strength += Math.min(data.pastGiftsGiven?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
    strength += Math.min(data.generalNotes?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;
    strength += Math.min(data.customDates?.length || 0, MAX_ITEMS_FOR_POINTS) * POINTS_PER_ITEM_ARRAY;

    return Math.min(Math.round(strength), 100);
  };

  const overallCompletion = calculateProfileStrength(formData);
  
  // Animated progress value for smooth transitions
  const animatedStrength = useSharedValue(0);
  
  React.useEffect(() => {
    animatedStrength.value = withSpring(overallCompletion, {
      damping: 15,
      stiffness: 100,
    });
  }, [overallCompletion]);

  // Animated styles for progress bar and text
  const strengthIndicatorStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        '#4ADE80', // Bright green for 100%
      ]
    );
    return { 
      width: `${animatedStrength.value}%`, 
      backgroundColor: color 
    };
  });

  const strengthTextStyle = useAnimatedStyle(() => {
    const color = interpolateColor(
      animatedStrength.value,
      [0, 33, 67, 100],
      [
        themedColors.error,
        themedColors.warning,
        themedColors.success,
        '#4ADE80',
      ]
    );
    return { color };
  });
  
  // Calculate completed sections for display (simplified approach)
  const totalSections = 8; // Basic, Interests, Preferences, Wishlist, Past Gifts, Sizes, Notes, Custom Dates
  let completedSectionsCount = 0;
  
        // Count completed sections based on data presence
   if (formData.birthday || formData.anniversary) completedSectionsCount++;
   
   // Check if interests/dislikes have actual content (not just empty strings)
   const hasInterests = formData.interestsInput && formData.interestsInput.split(',').map(item => item.trim()).filter(Boolean).length > 0;
   const hasDislikes = formData.dislikesInput && formData.dislikesInput.split(',').map(item => item.trim()).filter(Boolean).length > 0;
   if (hasInterests || hasDislikes) completedSectionsCount++;
   if (formData.preferences?.favoriteColor || formData.preferences?.preferredStyle || 
       (formData.preferences?.favoriteBrands && formData.preferences.favoriteBrands.length > 0)) completedSectionsCount++;
   if (formData.wishlistItems && formData.wishlistItems.length > 0) completedSectionsCount++;
   if (formData.pastGiftsGiven && formData.pastGiftsGiven.length > 0) completedSectionsCount++;
   if (formData.clothingSize || formData.shoeSize) completedSectionsCount++;
   if (formData.generalNotes && formData.generalNotes.length > 0) completedSectionsCount++;
   if (formData.customDates && formData.customDates.length > 0) completedSectionsCount++;
  


  // Get motivational message based on completion
  const getMotivationalMessage = (): { title: string; subtitle: string; icon: string } => {
    if (overallCompletion >= 90) {
      return {
        title: "Outstanding Profile! 🎉",
        subtitle: "You'll get amazing personalized gift recommendations",
        icon: "award"
      };
    } else if (overallCompletion >= 70) {
      return {
        title: "Great Progress! 👏",
        subtitle: "Your profile is looking excellent for gift ideas",
        icon: "trending-up"
      };
    } else if (overallCompletion >= 50) {
      return {
        title: "Keep Going! 💪",
        subtitle: "You're halfway there - add more for better suggestions",
        icon: "target"
      };
    } else if (overallCompletion >= 25) {
      return {
        title: "Good Start! 🌱",
        subtitle: "Add interests and preferences for personalized ideas",
        icon: "compass"
      };
    } else {
      return {
        title: "Let's Get Started! ✨",
        subtitle: "Fill in basic details to unlock gift recommendations",
        icon: "play-circle"
      };
    }
  };

  const motivationalContent = getMotivationalMessage();



  return (
    <Animated.View 
      entering={FadeIn.duration(600)}
      className={`p-6 bg-gradient-to-br rounded-xl border border-border from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 dark:border-primary-800 ${className}`}
    >
      {/* Header */}
      <View className="flex-row justify-between items-center mb-6">
        <View className="flex-1">
          <Text 
            className="text-lg font-bold"
            style={{ color: themedColors.textPrimary }}
          >
            {motivationalContent.title}
          </Text>
          <Text 
            className="mt-2 text-sm"
            style={{ color: themedColors.textSecondary }}
          >
            {motivationalContent.subtitle}
          </Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View className="mb-5">
        <View className="flex-row justify-between items-center mb-3">
          <Text 
            className="text-sm font-medium"
            style={{ color: themedColors.textSecondary }}
          >
            Profile Completion
          </Text>
          <Animated.Text
            style={strengthTextStyle}
            className="text-sm font-bold"
          >
            {overallCompletion}%
          </Animated.Text>
        </View>
        
        <View 
          className={`w-full h-3 rounded-full overflow-hidden border border-border ${
            isDark ? 'bg-gray-700' : 'bg-gray-200'
          }`}
        >
          <Animated.View 
            style={strengthIndicatorStyle}
            className="h-full rounded-full"
          />
        </View>
      </View>

      {/* Section Summary */}
      <View className="flex-row justify-between items-center">
        <Text 
          className="text-xs"
          style={{ color: themedColors.textSecondary }}
        >
          {completedSectionsCount} of {totalSections} sections completed
        </Text>
        <View className="flex-row items-center">
          <Feather name="gift" size={14} color={themedColors.accent} />
          <Text 
            className="ml-2 text-xs font-medium"
            style={{ color: themedColors.accent }}
          >
            Better gifts ahead!
          </Text>
        </View>
      </View>
    </Animated.View>
  );
};

export default ProfileCompletionProgress; 
import React, { useState } from 'react';
import { View, Text, Pressable, Image, ScrollView } from 'react-native';
import { Link } from 'expo-router'; // Import Link for navigation
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, {
  FadeInDown
} from 'react-native-reanimated';
import Input from '@/components/ui/Input'; // Assuming alias setup
import Button from '@/components/ui/Button'; // Assuming alias setup
import { useAuth } from '../../contexts/AuthContext'; // Import useAuth
import { getErrorDisplayMessage } from '../../utils/authErrorHandling';
import { useOptimizedAnimations } from '../../hooks/useOptimizedAnimations';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // State for login errors
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, signInWithGoogle, isLoading } = useAuth(); // Get signIn, signInWithGoogle, and isLoading from context

  // Optimized animations for better performance
  const { headerAnimatedStyle, formAnimatedStyle, footerAnimatedStyle } = useOptimizedAnimations();

  const handleLogin = async () => {
    // Light haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setError(null); // Clear previous errors

    // --- Start Input Validation ---
    if (!email || !password) {
      setError('Email and password are required.');
      // Error haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    // --- End Input Validation ---

    try {
      await signIn(email, password);
      // Success haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Login failed:', err);
      // Enhanced error handling with user-friendly messages
      const errorMessage = getErrorDisplayMessage(err.code, err.message);
      setError(errorMessage);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const handleGoogleSignIn = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setError(null); // Clear previous errors
    console.log('Google Sign-in attempt...');
    try {
      await signInWithGoogle();
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Google Sign-in failed:', err);
      // Enhanced Google sign-in error handling
      const errorMessage = getErrorDisplayMessage(err.code || 'google/unknown-error', err.message);
      setError(errorMessage);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const togglePasswordVisibility = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowPassword(!showPassword);
  };

  return (
    <ScrollView 
      className="flex-1 bg-background"
      contentContainerStyle={{ flexGrow: 1 }}
      showsVerticalScrollIndicator={false}
    >
      <View className="flex-1 px-6 pt-12 pb-8">
        {/* Header Section */}
        <Animated.View 
          className="items-center mb-12"
          style={headerAnimatedStyle}
        >
          {/* Logo */}
          <View className="mb-6">
            <Image 
              source={require('@/assets/images/icon.png')}
              className="w-24 h-24"
              resizeMode="contain"
            />
          </View>
          
          {/* Welcome Text */}
          <Text className="mb-3 text-3xl font-bold text-center text-text-primary">
            Welcome to Giftmi
          </Text>
          
          {/* Tagline */}
          <Text className="px-4 text-lg font-medium text-center text-text-secondary">
            Your personal gift-giving assistant
          </Text>
        </Animated.View>

        {/* Login Form Container */}
        <View className="flex-1 justify-center">
          <Animated.View 
            className="mx-auto w-full max-w-sm"
            style={formAnimatedStyle}
          >
            {/* Form Title */}
            <Text className="mb-8 text-2xl font-bold text-center text-text-primary">
              Sign In
            </Text>

            {/* Input Fields with improved spacing */}
            <View className="gap-6 mb-6">
              <Input
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!isLoading}
                leftIcon={
                  <Ionicons 
                  className='ml-2'
                    name="mail-outline" 
                    size={20} 
                    color="#7A3E4F" 
                  />
                }
              />
              <Input
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                editable={!isLoading}
                leftIcon={
                  <Ionicons 
                  className='ml-2'
                    name="lock-closed-outline" 
                    size={20} 
                    color="#7A3E4F" 
                  />
                }
                rightIcon={
                  <Ionicons 
                    name={showPassword ? "eye-off-outline" : "eye-outline"} 
                    size={20} 
                    color="#7A3E4F" 
                    className='mr-2'
                  />
                }
                onRightIconPress={togglePasswordVisibility}
              />
            </View>

            {/* Display login error message */}
            {error && (
              <Animated.View 
                className="p-4 mb-6 bg-red-50 rounded-xl border border-red-200"
                entering={FadeInDown.duration(300)}
              >
                <View className="flex-row items-center">
                  <Ionicons 
                    name="alert-circle-outline" 
                    size={20} 
                    color="#D90429" 
                    style={{ marginRight: 8 }}
                  />
                  <Text className="flex-1 text-sm text-feedback-error">
                    {error}
                  </Text>
                </View>
              </Animated.View>
            )}

            {/* Login Button */}
            <Button
              title="Sign In"
              onPress={handleLogin}
              className="mb-4 rounded-xl"
              isLoading={isLoading}
              disabled={isLoading}
              variant="primary"
            />

            {/* Forgot Password Link */}
            <Link href="/forgot-password" asChild>
              <Pressable disabled={isLoading} className="mb-6">
                <Text className="text-sm text-center text-primary-500 underline">
                  Forgot your password?
                </Text>
              </Pressable>
            </Link>

            {/* Separator */}
            <View className="flex-row items-center my-6">
              <View className="flex-1 h-px bg-border" />
              <Text className="mx-4 text-sm font-medium text-text-secondary">OR</Text>
              <View className="flex-1 h-px bg-border" />
            </View>

            {/* Google Sign-in Button */}
            <Button
              title="Continue with Google"
              onPress={handleGoogleSignIn}
              variant="secondary" 
              className="mb-8 rounded-xl" 
              isLoading={isLoading} 
              disabled={isLoading}
              leftIcon={
                <Ionicons 
                  name="logo-google" 
                  size={18} 
                  color="#FFFFFF" 
                />
              }
            />
          </Animated.View>
        </View>

        {/* Footer Links */}
        <Animated.View 
          className="pt-8 mt-auto"
          style={footerAnimatedStyle}
        >
          <Link href="/signup" asChild>
            <Pressable disabled={isLoading} className="p-4">
              <Text className="text-base text-center text-text-secondary">
                Don't have an account?{' '}
                <Text className="font-semibold underline text-primary-500">
                  Sign Up
                </Text>
              </Text>
            </Pressable>
          </Link>
        </Animated.View>
      </View>
    </ScrollView>
  );
}
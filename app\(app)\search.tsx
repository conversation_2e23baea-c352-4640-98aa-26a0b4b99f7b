import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { View, Text, ActivityIndicator, SafeAreaView, StatusBar, ScrollView, Alert, TouchableOpacity, Image } from 'react-native';
import { useSearchRecommendations, SearchMode } from '../../hooks/useSearchRecommendations';
import {
  GiftRecommendation,
  FeedbackEntry,
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
} from '../../services/recommendationService';
import Input from '../../components/ui/Input';
import CustomButton from '../../components/ui/Button';
import GiftGallery from '../../components/home/<USER>';
import SearchModeToggle from '../../components/ui/SearchModeToggle';
import ProfileDropdown from '../../components/ui/ProfileDropdown';
import { useThemeManager } from '../../hooks/useThemeManager';
import useCalendarData from '../../hooks/useCalendarData';
import { Feather } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import * as Haptics from 'expo-haptics';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useRouter } from 'expo-router';
import { Timestamp } from 'firebase/firestore';
import { updateSignificantOther } from '../../services/profileService';
import ActionMenu from '../../components/home/<USER>';
import { AddGeneralNoteModal } from '../../components/profile/AddGeneralNoteModal';
import AddCustomDateModal from '../../components/profile/AddCustomDateModal';
import AddPastGiftModal from '../../components/profile/AddPastGiftModal';
import { useRecommendationFeedback } from '@/hooks/useRecommendationFeedback';
import searchicon from '@/assets/images/search.png';

// --- Centralized AppColors (Placeholder - Integrate with your actual theme) ---
const AppColors = {
  primary: '#E87900',
  primaryDark: '#D96D00',
  primaryContent: '#FFFFFF', // For text/icons on primary background

  background: '#F3F4F6',
  backgroundDark: '#111827',
  
  inputBackground: '#FFFFFF', // Example
  inputBackgroundDark: '#1F2937', // Example

  textPrimary: '#1F2937',
  textPrimaryDark: '#F9FAFB',
  textSecondary: '#6B7280',
  textSecondaryDark: '#9CA3AF',
  
  error: '#EF4444',
  errorDark: '#F87171',
  errorBackground: '#FEE2E2', // Example: bg-error/10
  errorBackgroundDark: '#4A1313', // Example: bg-error-dark/10 (approx)

  gray100: '#F3F4F6',
  gray400: '#9CA3AF',
  gray800: '#1F2937',

  white: '#FFFFFF', // Absolute white
  // ... other necessary colors
};
// --- End AppColors ---

const SearchScreen = () => {
  const [query, setQuery] = useState('');
  const { colorScheme } = useThemeManager();
  const { profiles, selectedProfileId, handleProfileSelect } = useCalendarData();
  const { 
    recommendations, 
    isGenerating, 
    recommendationError, 
    searchMode,
    setSearchMode,
    fetchRecommendations 
  } = useSearchRecommendations();
  const { user } = useAuth();
  const router = useRouter();
  
  const {
    currentFeedbackMap,
    feedbackError,
    handleFeedback,
    fetchProfileFeedback,
  } = useRecommendationFeedback(selectedProfileId);
  
  // + Button and Action Menu state
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isDateModalVisible, setIsDateModalVisible] = useState(false);
  const [isGiftModalVisible, setIsGiftModalVisible] = useState(false);

  // Animation values for + button
  const plusIconRotation = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);

  const isDark = colorScheme === 'dark';

  // MEDIUM 1: Use themedColors for consistent theming
  const themedColors = useMemo(() => ({
    primary: isDark ? AppColors.primaryDark : AppColors.primary,
    primaryContent: AppColors.primaryContent, // Assuming white text on primary for both modes
    background: isDark ? AppColors.backgroundDark : AppColors.background,
    inputBackground: isDark ? AppColors.inputBackgroundDark : AppColors.inputBackground,
    textPrimary: isDark ? AppColors.textPrimaryDark : AppColors.textPrimary,
    textSecondary: isDark ? AppColors.textSecondaryDark : AppColors.textSecondary,
    error: isDark ? AppColors.errorDark : AppColors.error,
    errorBackground: isDark ? AppColors.errorBackgroundDark : AppColors.errorBackground,
    placeholder: isDark ? AppColors.textSecondaryDark : AppColors.textSecondary,
    gray400: AppColors.gray400, // For specific neutral icons
    initialIconBackground: isDark ? AppColors.primaryDark+'33' : AppColors.primary+'1A', // Primary with opacity
    noResultsIconBackground: isDark ? AppColors.gray800 : AppColors.gray100,
  }), [isDark]);

  // Animation effects for + button
  useEffect(() => {
    plusIconRotation.value = withTiming(isMenuVisible ? 45 : 0, {
      duration: 200,
    });
    backdropOpacity.value = withTiming(isMenuVisible ? 0.5 : 0, {
      duration: 200,
    });
  }, [isMenuVisible, plusIconRotation, backdropOpacity]);

  // Animated styles
  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
    pointerEvents: isMenuVisible ? 'auto' : 'none',
  }));
  
  const plusIconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${plusIconRotation.value}deg` }],
  }));

  // Load feedback when profile changes
  useEffect(() => {
    if (selectedProfileId) {
      fetchProfileFeedback();
    }
  }, [selectedProfileId, fetchProfileFeedback]);

  // + Button and Modal handlers
  const handleNavigateToAddProfile = () => {
    router.push('/profiles/add');
  };

  const handleNavigateToEditProfile = () => {
    if (selectedProfileId) {
      router.push(`/profiles/${selectedProfileId}/edit`);
    }
  };

  const handleOpenNoteModal = () => {
    setIsNoteModalVisible(true);
  };

  const handleOpenDateModal = () => {
    setIsDateModalVisible(true);
  };

  const handleOpenGiftModal = () => {
    setIsGiftModalVisible(true);
  };

  const handleSaveNote = async (noteText: string) => {
    if (!user || !selectedProfileId || !noteText.trim()) {
      console.error('User not authenticated, no profile selected, or empty note.');
      return;
    }

    try {
      const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
      if (!currentProfile) {
        console.error('Selected profile not found.');
        return;
      }

      const newNote = {
        note: noteText.trim(),
        date: Timestamp.now(),
      };

      const updatedNotes = [
        ...(currentProfile.generalNotes || []),
        newNote,
      ];
      const updatedProfile = {
        ...currentProfile,
        generalNotes: updatedNotes,
      };

      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      setIsNoteModalVisible(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error adding note:', error);
      Alert.alert('Error', 'Failed to add note. Please try again.');
    }
  };

  const handleSaveDate = async (dateData: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (!user || !selectedProfileId || !dateData.date) {
      console.error('User not authenticated, no profile selected, or no date provided.');
      return;
    }

    try {
      const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
      if (!currentProfile) {
        console.error('Selected profile not found.');
        return;
      }

      const newCustomDate = {
        id: uuidv4(),
        name: dateData.name,
        date: Timestamp.fromDate(dateData.date),
      };

      const updatedCustomDates = [
        ...(currentProfile.customDates || []),
        newCustomDate,
      ];
      const updatedProfile = {
        ...currentProfile,
        customDates: updatedCustomDates,
      };

      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      setIsDateModalVisible(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error adding date:', error);
      Alert.alert('Error', 'Failed to add date. Please try again.');
    }
  };

  const handleSaveGift = async (giftData: {
    item: string;
    occasion?: string;
    date: Date | null;
    reaction?: string;
  }) => {
    if (!user || !selectedProfileId || !giftData.item.trim()) {
      console.error('User not authenticated, no profile selected, or empty gift item.');
      return;
    }

    try {
      const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
      if (!currentProfile) {
        console.error('Selected profile not found.');
        return;
      }

      const newPastGift = {
        item: giftData.item.trim(),
        occasion: giftData.occasion,
        date: giftData.date ? Timestamp.fromDate(giftData.date) : null,
        reaction: giftData.reaction,
      };

      const updatedPastGifts = [
        ...(currentProfile.pastGiftsGiven || []),
        newPastGift,
      ];
      const updatedProfile = {
        ...currentProfile,
        pastGiftsGiven: updatedPastGifts,
      };

      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      setIsGiftModalVisible(false);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error adding past gift:', error);
      Alert.alert('Error', 'Failed to add past gift. Please try again.');
    }
  };

  // Helper function to get the selected profile name
  const getSelectedProfileName = () => {
    if (!selectedProfileId || !profiles) return undefined;
    return profiles.find(p => p.profileId === selectedProfileId)?.name;
  };

  // Dynamic placeholder and empty state message
  const searchPlaceholder = useMemo(() => {
    if (searchMode === 'personalized') {
      const name = getSelectedProfileName();
      return name
        ? `Search for a gift idea for \"${name}\"...`
        : 'Search for a gift idea for someone special...';
    }
    return 'Search for a general gift idea...';
  }, [searchMode, selectedProfileId, profiles]);

  const emptyStateMessage = useMemo(() => {
    if (searchMode === 'personalized') {
      const name = getSelectedProfileName();
      return name
        ? `Enter a query above to search for gift ideas for \"${name}\".`
        : 'Enter a query above to search for a gift idea for someone special.';
    }
    return 'Enter a query above to search for general gift ideas.';
  }, [searchMode, selectedProfileId, profiles]);

  const handleSearch = () => {
    if (query.trim()) {
      fetchRecommendations(query.trim(), selectedProfileId);
    }
  };

  const renderContent = () => {
    if (isGenerating) {
      return (
        <View className="flex-1 justify-center items-center pt-12">
          <ActivityIndicator size="large" color={themedColors.primary} />
          <Text className="mt-4 text-base font-medium" style={{ color: themedColors.textSecondary }}>
            Finding gifts...
          </Text>
        </View>
      );
    }

    if (recommendationError) {
      // LOW 3: User-friendly error message
      console.error("Search failed:", recommendationError); // Log detailed error for devs
      return (
        <View className="flex-1 justify-center items-center px-6 pt-12">
          <View className="justify-center items-center mb-4 w-16 h-16 rounded-full" style={{ backgroundColor: themedColors.errorBackground }}>
            <Feather name="alert-triangle" size={28} color={themedColors.error} />
          </View>
          <Text className="mb-2 text-lg font-medium text-center" style={{ color: themedColors.error }}>
            Search Failed
          </Text>
          <Text className="text-base text-center" style={{ color: themedColors.textSecondary }}>
            Sorry, we couldn't fetch results. Please check your connection and try again.
          </Text>
        </View>
      );
    }

    if (recommendations && recommendations.length > 0) {
      // MEDIUM 2: Pass actual recommendationError to RecommendationDisplay
      // LOW 4: isGenerating will be false here, so isLoading on RecommendationDisplay is effectively false.
      // This is fine if RecommendationDisplay doesn't have its own internal loading states independent of the main search.
      return (
        <GiftGallery
          recommendations={recommendations}
          isLoading={isGenerating}
          error={recommendationError}
          currentFeedbackMap={currentFeedbackMap}
          onFeedback={handleFeedback}
          onGenerateNewIdeas={handleSearch}
        />
      );
    }

    if (recommendations && recommendations.length === 0) {
      return (
        <View className="flex-1 justify-center items-center px-6 pt-12">
          <View className="justify-center items-center mb-4 w-16 h-16 rounded-full" style={{ backgroundColor: themedColors.noResultsIconBackground }}>
            <Feather name="search" size={28} color={themedColors.gray400} />
          </View>
          <Text className="mb-2 text-lg font-medium text-center" style={{ color: themedColors.textPrimary }}>
            No Results Found
          </Text>
          <Text className="text-base text-center" style={{ color: themedColors.textSecondary }}>
            Try broadening your search terms or checking for typos.
          </Text>
        </View>
      );
    }

    // Initial state (before any search or after clearing query)
    return (
      <View className="flex-1 justify-center items-center px-6 pt-12">
        <View className="justify-center items-center mb-4 w-16 h-16 rounded-full border border-black bg-primary-500/20">
          <Image source={searchicon} style={{ width: 156, height: 156 }} />
        </View>
        <Text className="mt-12 text-lg text-center" style={{ color: themedColors.textSecondary }}>
          {emptyStateMessage}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      
      {/* Backdrop overlay for action menu */}
      <Animated.View
        style={backdropAnimatedStyle}
        className="absolute inset-0 z-20 bg-black"
        onTouchEnd={() => setIsMenuVisible(false)}
      />

      {/* Action Menu */}
      {isMenuVisible && (
        <ActionMenu
          className="absolute right-1 top-20 z-40"
          onClose={() => setIsMenuVisible(false)}
          onAddProfile={handleNavigateToAddProfile}
          onAddNote={handleOpenNoteModal}
          onAddDate={handleOpenDateModal}
          onAddGift={handleOpenGiftModal}
          onEditProfile={handleNavigateToEditProfile}
        />
      )}
      
      {/* Wrapped content in ScrollView to ensure search bar is always visible if keyboard appears */}
      <ScrollView 
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }} 
        className="p-5" 
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled" // Good for usability with inputs
      >
        <View className="flex-row justify-between items-center mb-6">
          <Text className="text-2xl font-bold text-primary-500" >
            Gift Search
          </Text>
          {profiles.length > 0 && (
            <View className="relative w-1/2">
              <ProfileDropdown
                profiles={profiles}
                selectedProfileId={selectedProfileId}
                onProfileSelect={handleProfileSelect}
                trigger={
                  <View className="flex-row justify-between items-center p-2 rounded-xl border shadow-sm border-border dark:border-border-dark bg-card dark:bg-card-dark">
                    <Text className="text-text-secondary dark:text-text-secondary-dark">
                      Profile:{' '}
                    </Text>
                    <Text className="text-base font-medium text-primary-500">
                     {getSelectedProfileName() || 'Select Profile'}
                    </Text>
                    <Feather name="chevron-down" size={20} color={isDark ? '#C70039' : '#A3002B'} />
                  </View>
                }
              />
            </View>
          )}
          <TouchableOpacity
            onPress={() => setIsMenuVisible((prev) => !prev)}
            testID="plus-icon-button"
          >
            <Animated.View style={plusIconAnimatedStyle}>
              <Feather
                name="plus"
                size={28}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Search Mode Toggle */}
        <SearchModeToggle
          searchMode={searchMode}
          onToggle={setSearchMode}
          profileName={getSelectedProfileName()}
          disabled={!selectedProfileId}
        />

        {/* Profile requirement notice */}
        {!selectedProfileId && (
          <View className="p-3 mb-4 rounded-md bg-warning/20">
            <Text className="text-sm text-center text-warning">
              Select a profile to save your feedback on recommendations
            </Text>
          </View>
        )}

        {/* Feedback Error */}
        {feedbackError && (
          <View className="p-3 mb-4 rounded-md bg-error/20">
            <Text className="text-sm text-center text-error">
              {feedbackError}
            </Text>
          </View>
        )}

        <View className="flex-row gap-3 items-center">
          <View className="flex-1">
            <Input
              placeholder={searchPlaceholder}
              value={query}
              onChangeText={setQuery}
              keyboardType="default"
              autoCapitalize="none"
              returnKeyType="search"
              onSubmitEditing={handleSearch}
            />
          </View>
          
          {/* LOW 2: Assuming CustomButton uses NativeWind classes for theming primarily */}
          <CustomButton // Renamed to Button if it's your standard app button
            onPress={handleSearch}
            disabled={isGenerating || !query.trim()}
            title="Search"
            // className should ideally make it use themed primary colors via NativeWind config
            className="px-4 py-3 mb-4 rounded-lg bg-primary dark:bg-primary-dark" 
            textClassName="text-white font-medium text-base" // Assuming white text for primary button
            // LOW 1: Use themed color for icon
            leftIcon={<Feather name="search" size={18} color={themedColors.primaryContent} />}
          />
        </View>

        <View className="flex-1">
          {renderContent()}
        </View>
      </ScrollView>

      {/* Modal Components */}
      <AddGeneralNoteModal
        isVisible={isNoteModalVisible}
        onClose={() => setIsNoteModalVisible(false)}
        onSave={handleSaveNote}
      />
      <AddCustomDateModal
        isVisible={isDateModalVisible}
        onClose={() => setIsDateModalVisible(false)}
        onAddItem={handleSaveDate}
        profileId={selectedProfileId}
      />
      <AddPastGiftModal
        isVisible={isGiftModalVisible}
        onClose={() => setIsGiftModalVisible(false)}
        onAddItem={handleSaveGift}
      />
    </SafeAreaView>
  );
};

export default SearchScreen;
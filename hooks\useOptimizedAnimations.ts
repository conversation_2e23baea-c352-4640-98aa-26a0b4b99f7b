import { useEffect, useMemo } from 'react';
import { useSharedValue, useAnimatedStyle, withTiming, withDelay, interpolate } from 'react-native-reanimated';

interface AnimationConfig {
  duration?: number;
  delay?: number;
  translateY?: number;
  enabled?: boolean;
}

interface UseOptimizedAnimationsProps {
  headerConfig?: AnimationConfig;
  formConfig?: AnimationConfig;
  footerConfig?: AnimationConfig;
}

export const useOptimizedAnimations = ({
  headerConfig = {},
  formConfig = {},
  footerConfig = {}
}: UseOptimizedAnimationsProps = {}) => {
  // Default configurations optimized for performance
  const defaultConfig = {
    duration: 400, // Reduced from 600ms for better performance
    delay: 100,     // Reduced from 200ms
    translateY: 20, // Reduced from 30px for subtler effect
    enabled: true
  };

  const header = { ...defaultConfig, ...headerConfig };
  const form = { ...defaultConfig, delay: 150, ...formConfig };
  const footer = { ...defaultConfig, delay: 200, translateY: 15, ...footerConfig };

  // Shared values
  const headerAnimation = useSharedValue(0);
  const formAnimation = useSharedValue(0);
  const footerAnimation = useSharedValue(0);

  // Memoized animated styles to prevent unnecessary recalculations
  const headerAnimatedStyle = useMemo(() => 
    useAnimatedStyle(() => {
      if (!header.enabled) return { opacity: 1, transform: [] };
      
      return {
        opacity: headerAnimation.value,
        transform: [
          {
            translateY: interpolate(
              headerAnimation.value, 
              [0, 1], 
              [header.translateY, 0]
            ),
          },
        ],
      };
    }), [header.enabled, header.translateY]
  );

  const formAnimatedStyle = useMemo(() =>
    useAnimatedStyle(() => {
      if (!form.enabled) return { opacity: 1, transform: [] };
      
      return {
        opacity: formAnimation.value,
        transform: [
          {
            translateY: interpolate(
              formAnimation.value, 
              [0, 1], 
              [form.translateY, 0]
            ),
          },
        ],
      };
    }), [form.enabled, form.translateY]
  );

  const footerAnimatedStyle = useMemo(() =>
    useAnimatedStyle(() => {
      if (!footer.enabled) return { opacity: 1, transform: [] };
      
      return {
        opacity: footerAnimation.value,
        transform: [
          {
            translateY: interpolate(
              footerAnimation.value, 
              [0, 1], 
              [footer.translateY, 0]
            ),
          },
        ],
      };
    }), [footer.enabled, footer.translateY]
  );

  // Start animations on mount
  useEffect(() => {
    if (header.enabled) {
      headerAnimation.value = withTiming(1, { duration: header.duration });
    } else {
      headerAnimation.value = 1;
    }

    if (form.enabled) {
      formAnimation.value = withDelay(
        form.delay, 
        withTiming(1, { duration: form.duration })
      );
    } else {
      formAnimation.value = 1;
    }

    if (footer.enabled) {
      footerAnimation.value = withDelay(
        footer.delay, 
        withTiming(1, { duration: footer.duration })
      );
    } else {
      footerAnimation.value = 1;
    }
  }, [header.enabled, form.enabled, footer.enabled]);

  return {
    headerAnimatedStyle,
    formAnimatedStyle,
    footerAnimatedStyle,
    // Expose values for custom animations if needed
    headerAnimation,
    formAnimation,
    footerAnimation
  };
};

// Hook for simple fade-in animation (most performant)
export const useFadeInAnimation = (delay: number = 0, duration: number = 300) => {
  const opacity = useSharedValue(0);

  const animatedStyle = useMemo(() =>
    useAnimatedStyle(() => ({
      opacity: opacity.value,
    })), []
  );

  useEffect(() => {
    opacity.value = withDelay(delay, withTiming(1, { duration }));
  }, [delay, duration]);

  return animatedStyle;
};

// Hook for error animations (lightweight)
export const useErrorAnimation = () => {
  const shake = useSharedValue(0);

  const animatedStyle = useMemo(() =>
    useAnimatedStyle(() => ({
      transform: [
        {
          translateX: interpolate(
            shake.value,
            [0, 0.25, 0.5, 0.75, 1],
            [0, -5, 5, -5, 0]
          ),
        },
      ],
    })), []
  );

  const triggerShake = () => {
    shake.value = withTiming(1, { duration: 400 }, () => {
      shake.value = 0;
    });
  };

  return { animatedStyle, triggerShake };
};

import { Stack } from 'expo-router';
import { useThemeManager } from "../../../hooks/useThemeManager";  

export default function SettingsLayout() {
  const { colorScheme } = useThemeManager();

  return (
    <Stack
      screenOptions={{
        headerLargeTitle: true,
        headerLargeTitleStyle: { color: '#A3002B' },
        headerTitleStyle: { color: '#A3002B', fontWeight: '600' },
        headerStyle: { backgroundColor: colorScheme === 'dark' ? '#2F1E23' : '#F9FAFB' },
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen name="index" options={{ title: 'Settings', headerShown: false }} />
      <Stack.Screen name="personal-info" options={{ title: 'Personal Information' }} />
      <Stack.Screen name="notifications" options={{ title: 'Notification Settings' }} />
      <Stack.Screen name="privacy-security" options={{ title: 'Privacy & Security' }} />
      <Stack.Screen name="help-support" options={{ title: 'Help & Support' }} />
      {/* Add other settings screens here later */}
    </Stack>
  );
}
